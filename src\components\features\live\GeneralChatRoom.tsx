"use client";

import { TabIndex } from "@/constants/tabs";
import { useAuth } from "@/contexts/AuthContext";
import { useAutoMessages } from "@/hooks/useAutoMessages";
import { useBanner } from "@/hooks/useBanner";
import { useRealtimeMessage } from "@/hooks/useRealtimeMessage";
import { ChatMessage as ChatMessageType } from "@/services/chatService";
import type {
  ChatMessage as ChatMessage<PERSON>pi,
  ChatRoom,
} from "@/types/chat.types";
import React, { useCallback, useEffect, useRef, useState } from "react";
import AuthModal from "../../AuthModal";
import BannerOdds from "../BannerOdds";
import ChatInput from "../ChatInput";
import ChatMessage from "../ChatMessage";
import { BannerData } from "@/types/banner";

interface GeneralChatRoomProps {
  roomId?: string;
  isLoggedIn: boolean;
  onOpenAuthModal: (mode: "login" | "register") => void;
  onCreateChat: (
    name: string,
    type: "direct" | "group" | "private"
  ) => Promise<
    | {
        error: Error;
        room?: undefined;
      }
    | {
        room: ChatRoom | null;
        error: null;
      }
  >;
  onTabChange: (tabIndex: TabIndex, roomId?: string) => void;
}

export default function GeneralChatRoom({
  roomId,
  isLoggedIn,
  onCreateChat,
  onTabChange,
}: GeneralChatRoomProps) {
  const { user, messagesLoading, loadMessagesForRoom, sendMessage } = useAuth();
  const [showScrollToBottom, setShowScrollToBottom] = useState(false);
  const [isLoadingMore, setIsLoadingMore] = useState(false);
  const [canLoadMore, setCanLoadMore] = useState(true);
  const [replyTo, setReplyTo] = useState<ChatMessageType | null>(null);

  // Local state for auto messages display
  const [autoMessages, setAutoMessages] = useState<ChatMessageType[]>([]);

  const { messages } = useRealtimeMessage(roomId ?? "");

  const {
    bannerData,
    openAuthModal,
    closeAuthModal,
    isAuthModalOpen,
    authMode,
    setAuthMode,
    hideBanner,
  } = useBanner();

  // Auto messages hook
  useAutoMessages({
    matchId: roomId ?? "",
    isEnabled: true,
    onAddMessage: (message: ChatMessageType) => {
      setAutoMessages((prev) => [...prev, message]);
    },
  });

  // Mobile viewport handling
  const [viewportHeight, setViewportHeight] = useState(0);
  const [isMobile, setIsMobile] = useState(false);
  const [keyboardHeight, setKeyboardHeight] = useState(0);
  const [isIOS, setIsIOS] = useState(false);
  const [isKeyboardOpen, setIsKeyboardOpen] = useState(false);

  const chatContainerRef = useRef<HTMLDivElement | null>(null);
  const messagesEndRef = useRef<HTMLDivElement | null>(null);

  // Get user data from context or localStorage as fallback
  const getUserData = useCallback(() => {
    // First try to get from context
    if (user) {
      return {
        userId: user.id,
        name:
          user.user_metadata?.full_name ||
          user.email?.split("@")[0] ||
          "Anonymous",
        email: user.email,
        verified: user.email_confirmed_at ? true : false,
        isAdmin: user.role === "admin", // You can add admin logic here
      };
    }

    // Fallback to localStorage for backward compatibility
    const storedUser = localStorage.getItem("userData");
    if (storedUser) {
      try {
        return JSON.parse(storedUser);
      } catch (error) {
        // Error parsing user data
      }
    }
    return null;
  }, [user]);

  // Handle viewport and mobile detection
  const updateViewportInfo = useCallback(() => {
    const vh = window.innerHeight;
    const vw = window.innerWidth;
    setViewportHeight(vh);
    setIsMobile(vw < 1024);

    // Detect iOS
    const isIOSDevice =
      /iPad|iPhone|iPod/.test(navigator.userAgent) ||
      (navigator.platform === "MacIntel" && navigator.maxTouchPoints > 1);
    setIsIOS(isIOSDevice);

    // Detect keyboard height on mobile
    if (vw < 1024) {
      if (isIOSDevice) {
        // iOS: Use visual viewport to detect keyboard
        const visualViewport = window.visualViewport;
        if (visualViewport) {
          const keyboardHeight = Math.max(
            0,
            window.innerHeight - visualViewport.height
          );
          setKeyboardHeight(keyboardHeight);
          setIsKeyboardOpen(keyboardHeight > 0);
        }
      } else {
        // Android: Use window height difference
        const initialHeight = window.visualViewport?.height || vh;
        const currentHeight = window.innerHeight;
        const keyboardHeight = Math.max(0, initialHeight - currentHeight);
        setKeyboardHeight(keyboardHeight);
        setIsKeyboardOpen(keyboardHeight > 0);
      }
    }
  }, []);

  const scrollToBottom = useCallback(() => {
    // Chỉ scroll phần chat, không ảnh hưởng trang chính
    if (chatContainerRef.current) {
      chatContainerRef.current.scrollTop =
        chatContainerRef.current.scrollHeight;
    }
  }, []);

  // Load messages for the current room
  useEffect(() => {
    if (roomId) {
      loadMessagesForRoom(roomId);
    }
  }, [roomId, loadMessagesForRoom]);

  // Initialize viewport info
  useEffect(() => {
    updateViewportInfo();

    // Listen for viewport changes
    const handleResize = () => updateViewportInfo();
    const handleOrientationChange = () => {
      setTimeout(updateViewportInfo, 100); // Delay to get accurate measurements
    };

    // Listen for visual viewport changes (keyboard on mobile)
    const handleVisualViewportChange = () => {
      if (window.visualViewport) {
        updateViewportInfo();
      }
    };

    // iOS specific keyboard events
    const handleIOSKeyboardShow = () => {
      setIsKeyboardOpen(true);
      setTimeout(updateViewportInfo, 100);
    };

    const handleIOSKeyboardHide = () => {
      setIsKeyboardOpen(false);
      setTimeout(updateViewportInfo, 100);
    };

    window.addEventListener("resize", handleResize);
    window.addEventListener("orientationchange", handleOrientationChange);

    // iOS keyboard events
    if (isIOS) {
      window.addEventListener("focusin", handleIOSKeyboardShow);
      window.addEventListener("focusout", handleIOSKeyboardHide);
    }

    if (window.visualViewport) {
      window.visualViewport.addEventListener(
        "resize",
        handleVisualViewportChange
      );
    }

    return () => {
      window.removeEventListener("resize", handleResize);
      window.removeEventListener("orientationchange", handleOrientationChange);

      if (isIOS) {
        window.removeEventListener("focusin", handleIOSKeyboardShow);
        window.removeEventListener("focusout", handleIOSKeyboardHide);
      }

      if (window.visualViewport) {
        window.visualViewport.removeEventListener(
          "resize",
          handleVisualViewportChange
        );
      }
    };
  }, [updateViewportInfo, isIOS]);

  // Merge real messages and auto messages, then sort by timestamp
  const allMessages = React.useMemo(() => {
    // Convert real messages from API format to ChatMessageType format
    const realMessagesConverted = messages.map((msg: ChatMessageApi) => {
      const userData = getUserData();
      const isOwnMessage = userData?.userId === msg.user_id;

      return {
        id: msg.id,
        message: msg.content,
        timestamp: new Date(msg.created_at).getTime(),
        userId: msg.user_id,
        userName: isOwnMessage ? "Bạn" : msg.user?.full_name || "Unknown User",
        userAvatar: msg.user?.avatar_url || "",
        userColor: "bg-blue-500",
        verified: false,
        isAdmin: msg.user?.role === "admin",
        replyTo: null,
        reactions: {} as { [key: string]: number },
        pinned: false,
        isAutoMessage: false,
        isJoinMessage: false,
      } as ChatMessageType;
    });

    // Combine real messages and auto messages
    const combined = [...realMessagesConverted, ...autoMessages];

    // Sort by timestamp (oldest first)
    return combined.sort((a, b) => a.timestamp - b.timestamp);
  }, [messages, autoMessages, getUserData]);

  // Auto-scroll to bottom when new messages arrive
  useEffect(() => {
    if (allMessages.length > 0) {
      const timer = setTimeout(() => {
        if (chatContainerRef.current) {
          chatContainerRef.current.scrollTop =
            chatContainerRef.current.scrollHeight;
        }
      }, 100);
      return () => clearTimeout(timer);
    }
  }, [allMessages.length]);

  // iOS: Scroll to bottom when keyboard opens
  useEffect(() => {
    if (isIOS && isKeyboardOpen && chatContainerRef.current) {
      const timer = setTimeout(() => {
        if (chatContainerRef.current) {
          chatContainerRef.current.scrollTop =
            chatContainerRef.current.scrollHeight;
        }
      }, 300); // Delay to ensure keyboard is fully open
      return () => clearTimeout(timer);
    }
  }, [isIOS, isKeyboardOpen]);

  const handleScroll = useCallback(() => {
    if (chatContainerRef.current) {
      const { scrollTop, scrollHeight, clientHeight } =
        chatContainerRef.current;
      const isScrolledUp = scrollTop < scrollHeight - clientHeight - 100;
      setShowScrollToBottom(isScrolledUp);

      // Check if we can load more messages
      if (
        scrollTop < 100 &&
        canLoadMore &&
        !isLoadingMore &&
        messages.length > 0
      ) {
        // loadOlderMessages();
      }
    }
  }, [canLoadMore, isLoadingMore, messages.length]);

  const handleSubmit = useCallback(
    async (messageText: string, replyToMessage?: ChatMessageType) => {
      if (!messageText.trim() || !isLoggedIn) return;

      const userData = getUserData();
      if (!userData) {
        openAuthModal("login");
        return;
      }
      if (!roomId) {
        return;
      }

      try {
        const { error } = await sendMessage(roomId, messageText);
        if (error) {
          // Failed to send message
        } else {
          // Clear reply state after successful send
          setReplyTo(null);
        }
      } catch (error) {
        // Error sending message
      }
    },
    [isLoggedIn, getUserData, roomId, openAuthModal, sendMessage]
  );

  const renderMessage = useCallback(
    (msg: ChatMessageType) => {
      const userData = getUserData();
      const isOwnMessage = userData?.userId === msg.userId;

      return (
        <ChatMessage
          key={msg.id}
          message={msg}
          isOwnMessage={false} // Always false to show all messages on the left
          onReply={(replyMessage: ChatMessageType) => {
            setReplyTo(replyMessage);
          }}
          onReact={(messageId: string, reactionType: string) => {
            // Handle reaction - you can implement this later
          }}
          onPin={(messageId: string, pinned: boolean) => {
            // Handle pin - you can implement this later
          }}
          onDelete={(messageId: string) => {
            // Handle delete - you can implement this later
          }}
          isAdmin={userData?.isAdmin || false}
        />
      );
    },
    [getUserData]
  );

  // Calculate dynamic styles for mobile
  const getChatContainerStyle = () => {
    if (isMobile && viewportHeight > 0) {
      const inputHeight = 60; // Approximate ChatInput height
      const safeAreaBottom = 20; // Safe area bottom

      let availableHeight;
      if (isIOS && isKeyboardOpen) {
        // iOS: Use visual viewport height when keyboard is open
        const visualViewport = window.visualViewport;
        if (visualViewport) {
          availableHeight =
            visualViewport.height - inputHeight - safeAreaBottom;
        } else {
          availableHeight = viewportHeight - inputHeight - safeAreaBottom;
        }
      } else {
        // Android or iOS without keyboard
        availableHeight =
          viewportHeight - inputHeight - safeAreaBottom - keyboardHeight;
      }

      return {
        height: `${Math.max(200, availableHeight)}px`,
        maxHeight: `${availableHeight}px`,
        paddingBottom: "20px",
        // Ensure scroll works on mobile
        overflowY: "auto" as const,
        WebkitOverflowScrolling: "touch" as const,
        overflowX: "hidden" as const,
        position: "relative" as const,
        flex: "1 1 auto",
      };
    }
    // Desktop: Ensure proper height and scroll
    return {
      height: "100%",
      paddingBottom: "20px",
      overflowY: "auto" as const,
    };
  };

  const getChatInputStyle = () => {
    if (isMobile && viewportHeight > 0) {
      let bottomPosition = "0px";

      if (isIOS && isKeyboardOpen) {
        // iOS: Position above keyboard using visual viewport
        const visualViewport = window.visualViewport;
        if (visualViewport) {
          bottomPosition = `${window.innerHeight - visualViewport.height}px`;
        }
      } else if (keyboardHeight > 0) {
        // Android: Use keyboard height
        bottomPosition = `${keyboardHeight}px`;
      }

      return {
        position: "fixed" as const,
        bottom: bottomPosition,
        left: "0",
        right: "0",
        zIndex: 50,
        borderTop: "1px solid var(--chat-input-border, #e5e7eb)",
        paddingBottom: "env(safe-area-inset-bottom, 10px)",
        boxShadow: "0 -2px 10px rgba(0, 0, 0, 0.1)",
      };
    }
    return {};
  };

  const onBannerClick = async () => {
    if (!user) {
      // User not logged in, show auth modal
      openAuthModal?.("login");
      return;
    }

    if (!bannerData) return;
    console.log("bannerData", bannerData);
    try {
      // Create 1-1 chat with admin
      const chatName = `Chat với ${bannerData.expert.name}`;
      const chatType = "direct";

      const { room, error } = await onCreateChat?.(chatName, chatType);

      if (error) {
        console.error("Error creating chat:", error);
        return;
      }

      if (room) {
        console.log("Banner clicked, calling hideBanner and changing tab");
        hideBanner();
        onTabChange?.(TabIndex.PRIVATE_CHAT, room?.id);
      }
    } catch (error) {
      console.error("Error handling banner click:", error);
    }
  };

  return (
    <>
      <div
        className="flex flex-col h-full relative"
        style={{
          paddingBottom: isMobile ? "0px" : "env(safe-area-inset-bottom, 0px)",
          // Mobile: Ensure proper height and allow scrolling
          ...(isMobile
            ? {
                position: "relative",
                height: "100%",
                overflow: "hidden", // Let child container handle scrolling
              }
            : {}),
        }}
      >
        <BannerOdds
          currentBanner={bannerData as unknown as BannerData}
          onBannerClick={onBannerClick}
        />
        {/* Messages Container */}
        <div
          ref={chatContainerRef}
          data-chat-container
          className="flex-1 overflow-y-auto px-2 sm:px-4 space-y-2 sm:space-y-3 scrollbar-hide"
          style={getChatContainerStyle()}
          onScroll={handleScroll}
        >
          {messagesLoading ? (
            <div className="flex justify-center items-center py-8">
              <div className="animate-spin rounded-full h-8 w-8 border-b-2 border-blue-500"></div>
            </div>
          ) : allMessages.length === 0 ? (
            <div className="text-center py-8 text-gray-500 dark:text-gray-400">
              <p>Chưa có tin nhắn nào. Hãy bắt đầu cuộc trò chuyện!</p>
            </div>
          ) : (
            allMessages.map(renderMessage)
          )}
          <div ref={messagesEndRef} />
        </div>

        {/* Scroll to bottom button */}
        {showScrollToBottom && (
          <button
            onClick={scrollToBottom}
            className="absolute bottom-20 right-4 bg-blue-500 hover:bg-blue-600 text-white p-2 rounded-full shadow-lg transition-all duration-200 z-10"
          >
            <svg
              className="w-5 h-5"
              fill="none"
              stroke="currentColor"
              viewBox="0 0 24 24"
            >
              <path
                strokeLinecap="round"
                strokeLinejoin="round"
                strokeWidth={2}
                d="M19 14l-7 7m0 0l-7-7m7 7V3"
              />
            </svg>
          </button>
        )}

        {/* Chat Input */}
        <div
          className="bg-white dark:bg-gray-800 border-t border-gray-200 dark:border-gray-700"
          style={getChatInputStyle()}
        >
          <ChatInput
            onSubmit={handleSubmit}
            isLoggedIn={isLoggedIn}
            onOpenAuthModal={openAuthModal}
            replyTo={replyTo}
            onCancelReply={() => setReplyTo(null)}
            placeholder="Nhập tin nhắn..."
          />
        </div>
        <AuthModal
          isOpen={isAuthModalOpen}
          onClose={closeAuthModal}
          initialMode={authMode}
          onModeChange={setAuthMode}
          onLoginSuccess={() => {}}
        />
      </div>
    </>
  );
}
