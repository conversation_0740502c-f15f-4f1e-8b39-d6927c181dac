'use client';

import Image from "next/image";
import Link from "next/link";
import { useState, useEffect } from "react";
import { useNews, useNewsFilters } from "@/hooks";
import type { PublishedPostWithSeo } from "@/types/news.types";

const CATS = ["Tất cả", "Bóng đá", "Chuyển nhượng", "Nhận định", "Esports"];

export default function NewsListPage() {
  const [selectedCategory, setSelectedCategory] = useState("Tất cả");
  const [searchQuery, setSearchQuery] = useState("");
  
  const { filters, queryParams, setFilters, setSearchQuery: setFiltersSearch, setCurrentPage } = useNewsFilters({
    published: true
  });
  
  const { publishedPosts, loading, error, totalCount, fetchPublishedPosts } = useNews();

  useEffect(() => {
    fetchPublishedPosts();
  }, [fetchPublishedPosts]);

  const totalPages = Math.ceil(totalCount / (queryParams.limit || 12));
  const currentPageNumber = queryParams.page || 1;

  // Cập nhật search query với debounce
  useEffect(() => {
    const timer = setTimeout(() => {
      setFiltersSearch(searchQuery);
    }, 500);
    return () => clearTimeout(timer);
  }, [searchQuery, setFiltersSearch]);

  // Format date helper
  const formatDate = (dateString: string) => {
    return new Date(dateString).toLocaleDateString('vi-VN', {
      day: '2-digit',
      month: '2-digit',
      year: 'numeric'
    });
  };

  // Handle pagination
  const handlePageChange = (page: number) => {
    if (page >= 1 && page <= totalPages) {
      setCurrentPage(page);
    }
  };

  const handlePrevPage = () => {
    if (currentPageNumber > 1) {
      setCurrentPage(currentPageNumber - 1);
    }
  };

  const handleNextPage = () => {
    if (currentPageNumber < totalPages) {
      setCurrentPage(currentPageNumber + 1);
    }
  };

  // Generate page numbers for pagination
  const getPageNumbers = () => {
    const pages = [];
    const maxVisiblePages = 5;
    
    if (totalPages <= maxVisiblePages) {
      // Show all pages if total is less than max visible
      for (let i = 1; i <= totalPages; i++) {
        pages.push(i);
      }
    } else {
      // Show first page, current page range, and last page
      if (currentPageNumber <= 3) {
        // Show first 4 pages + last page
        for (let i = 1; i <= 4; i++) {
          pages.push(i);
        }
        if (totalPages > 4) {
          pages.push('...');
          pages.push(totalPages);
        }
      } else if (currentPageNumber >= totalPages - 2) {
        // Show first page + last 4 pages
        pages.push(1);
        if (totalPages > 4) {
          pages.push('...');
        }
        for (let i = totalPages - 3; i <= totalPages; i++) {
          pages.push(i);
        }
      } else {
        // Show first page + current range + last page
        pages.push(1);
        pages.push('...');
        for (let i = currentPageNumber - 1; i <= currentPageNumber + 1; i++) {
          pages.push(i);
        }
        pages.push('...');
        pages.push(totalPages);
      }
    }
    
    return pages;
  };

  // Handle category filter
  const handleCategoryChange = (category: string) => {
    setSelectedCategory(category);
    if (category === "Tất cả") {
      setFilters({ search: undefined });
    } else {
      // Có thể mở rộng để filter theo category nếu có field category trong database
      setFilters({ search: category });
    }
  };

  if (error) {
    return (
      <div className="flex items-center justify-center min-h-[400px]">
        <div className="text-center">
          <p className="text-red-600 dark:text-red-400 mb-4">Có lỗi xảy ra: {error}</p>
          <button 
            onClick={fetchPublishedPosts}
            className="px-4 py-2 bg-blue-600 text-white rounded-md hover:bg-blue-700 transition-colors"
          >
            Thử lại
          </button>
        </div>
      </div>
    );
  }

  return (
    <>
      <section className="border-b border-gray-200 dark:border-gray-700 bg-white dark:bg-gray-900">
        <div className="py-4 sm:py-6 md:py-8">
          <h1 className="text-xl sm:text-2xl font-bold text-gray-900 dark:text-white">Tin tức</h1>
          <p className="mt-2 max-w-2xl text-sm sm:text-base text-gray-600 dark:text-gray-300">Cập nhật tin nóng, phân tích và nhận định mới nhất.</p>
          
          {/* Search bar */}
          <div className="mt-4">
            <input
              type="text"
              placeholder="Tìm kiếm tin tức..."
              value={searchQuery}
              onChange={(e) => setSearchQuery(e.target.value)}
              className="w-full max-w-md px-4 py-2 border border-gray-200 dark:border-gray-700 rounded-md bg-white dark:bg-gray-800 text-gray-900 dark:text-white placeholder-gray-500 dark:placeholder-gray-400 focus:outline-none focus:ring-2 focus:ring-blue-500"
            />
          </div>
        </div>
      </section>

      <section className="border-b border-gray-200 dark:border-gray-700 bg-white dark:bg-gray-900">
        <div className="py-2 sm:py-3">
          <div className="flex flex-wrap items-center gap-1 sm:gap-2">
            {CATS.map((c) => (
              <button 
                key={c} 
                onClick={() => handleCategoryChange(c)}
                className={`rounded-full px-2 sm:px-3 md:px-4 py-1 sm:py-1.5 text-xs sm:text-sm transition-colors ${
                  selectedCategory === c 
                    ? "bg-blue-600 text-white" 
                    : "bg-gray-100 dark:bg-gray-800 text-gray-700 dark:text-gray-300 hover:bg-gray-200 dark:hover:bg-gray-700"
                }`}
              >
                {c}
              </button>
            ))}
          </div>
        </div>
      </section>

      <section className="py-4 sm:py-6 bg-white dark:bg-gray-900">
        {loading ? (
          <div className="flex items-center justify-center min-h-[400px]">
            <div className="text-center">
              <div className="animate-spin rounded-full h-12 w-12 border-b-2 border-blue-600 mx-auto mb-4"></div>
              <p className="text-gray-600 dark:text-gray-300">Đang tải tin tức...</p>
            </div>
          </div>
        ) : publishedPosts.length === 0 ? (
          <div className="text-center py-12">
            <p className="text-gray-600 dark:text-gray-300">Không có tin tức nào được tìm thấy.</p>
          </div>
        ) : (
          <>
            <div className="grid grid-cols-1 gap-3 sm:gap-4 md:gap-5 sm:grid-cols-2 lg:grid-cols-2">
              {publishedPosts.map((post, index) => (
                <article key={post.post_id || `post-${index}`} className="overflow-hidden rounded-2xl border border-gray-200 dark:border-gray-700 bg-white dark:bg-gray-800 shadow-sm transition hover:shadow-md">
                  <Link href={`/tin-tuc/${post.slug || post.post_id}`} className="relative block aspect-[16/10] bg-white dark:bg-gray-700">
                    {post.thumbnail ? (
                      <Image src={post.thumbnail} alt={post.title} fill className="object-cover" />
                    ) : (
                      <div className="flex items-center justify-center h-full bg-gray-100 dark:bg-gray-700">
                        <span className="text-gray-400 dark:text-gray-300 text-sm">Không có ảnh</span>
                      </div>
                    )}
                  </Link>
                  <div className="p-3 sm:p-4">
                    <div className="mb-1 text-xs text-gray-500 dark:text-gray-400">
                      {post.author_name && `${post.author_name} • `}
                      {formatDate(post.published_at)}
                    </div>
                    <Link href={`/tin-tuc/${post.slug || post.post_id}`} className="line-clamp-2 text-sm sm:text-base font-semibold text-gray-900 dark:text-white hover:underline">
                      {post.title}
                    </Link>
                    {post.description && (
                      <p className="mt-1 line-clamp-2 text-xs sm:text-sm text-gray-600 dark:text-gray-300">
                        {post.description}
                      </p>
                    )}
                  </div>
                </article>
              ))}
            </div>
            
            {/* Pagination */}
            {totalPages > 1 && (
              <div className="flex justify-center items-center space-x-2 mt-8">
                {/* Previous button */}
                <button
                  onClick={handlePrevPage}
                  disabled={currentPageNumber === 1}
                  className={`px-3 py-2 rounded-md ${
                    currentPageNumber === 1
                      ? 'bg-gray-200 text-gray-400 cursor-not-allowed'
                      : 'bg-white text-gray-700 hover:bg-gray-50 border border-gray-300'
                  }`}
                >
                  Trước
                </button>

                {/* Page numbers */}
                {getPageNumbers().map((page, index) => (
                  <button
                    key={index}
                    onClick={() => typeof page === 'number' ? handlePageChange(page) : undefined}
                    disabled={typeof page !== 'number'}
                    className={`px-3 py-2 rounded-md ${
                      page === currentPageNumber
                        ? 'bg-blue-600 text-white'
                        : typeof page === 'number'
                        ? 'bg-white text-gray-700 hover:bg-gray-50 border border-gray-300'
                        : 'bg-transparent text-gray-400 cursor-default'
                    }`}
                  >
                    {page}
                  </button>
                ))}

                {/* Next button */}
                <button
                  onClick={handleNextPage}
                  disabled={currentPageNumber === totalPages}
                  className={`px-3 py-2 rounded-md ${
                    currentPageNumber === totalPages
                      ? 'bg-gray-200 text-gray-400 cursor-not-allowed'
                      : 'bg-white text-gray-700 hover:bg-gray-50 border border-gray-300'
                  }`}
                >
                  Sau
                </button>
              </div>
            )}
          </>
        )}
      </section>
    </>
  );
}
