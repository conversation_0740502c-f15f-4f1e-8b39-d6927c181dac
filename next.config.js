/** @type {import('next').NextConfig} */
const nextConfig = {
  images: {
    remotePatterns: [
      { protocol: 'https', hostname: 'ext.same-assets.com' },
      { protocol: 'https', hostname: 'api-ck.686868.me' },
      { protocol: 'https', hostname: 'images.dmca.com' },
      { protocol: 'https', hostname: 'images.unsplash.com' },
      { protocol: 'https', hostname: 'upload.wikimedia.org' },
      { protocol: 'https', hostname: 'g7w1r.4shares.live' },
      { protocol: 'https', hostname: '2987306724.global.cdnfastest.com' },
      { protocol: 'https', hostname: 'ngoaihangtv.xyz' },
    ],
    // Disable image caching
    minimumCacheTTL: 0,
    dangerouslyAllowSVG: true,
    unoptimized: true, // Disable image optimization to prevent caching
  },
  // Add no-cache headers
  async headers() {
    return [
      {
        source: '/_next/image(.*)',
        headers: [
          {
            key: 'Cache-Control',
            value: 'no-cache, no-store, must-revalidate',
          },
          {
            key: 'Pragma',
            value: 'no-cache',
          },
          {
            key: 'Expires',
            value: '0',
          },
        ],
      },
      {
        source: '/banner/(.*)',
        headers: [
          {
            key: 'Cache-Control',
            value: 'no-cache, no-store, must-revalidate',
          },
          {
            key: 'Pragma',
            value: 'no-cache',
          },
          {
            key: 'Expires',
            value: '0',
          },
        ],
      },
      {
        source: '/banner-chat/(.*)',
        headers: [
          {
            key: 'Cache-Control',
            value: 'no-cache, no-store, must-revalidate',
          },
          {
            key: 'Pragma',
            value: 'no-cache',
          },
          {
            key: 'Expires',
            value: '0',
          },
        ],
      },
    ];
  },

  webpack: (config, { dev }) => {
    if (dev) {
      config.plugins = config.plugins.filter(
        (plugin) => plugin.constructor.name !== 'ReactRefreshPlugin'
      )
    }
    return config
  },
};

module.exports = nextConfig;
