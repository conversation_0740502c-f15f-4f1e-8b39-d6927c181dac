import { useState, useEffect, useCallback } from 'react';
import { newsService } from '@/services/news.service';
import type { PostWithSeo } from '@/types/news.types';

export interface UseNewsDetailReturn {
  // State
  post: PostWithSeo | null;
  loading: boolean;
  error: string | null;
  
  // Actions
  fetchPost: (id: string) => Promise<void>;
  fetchPostBySlug: (slug: string) => Promise<void>;
  clearPost: () => void;
}

export function useNewsDetail(): UseNewsDetailReturn {
  const [post, setPost] = useState<PostWithSeo | null>(null);
  const [loading, setLoading] = useState(false);
  const [error, setError] = useState<string | null>(null);

  const fetchPost = useCallback(async (id: string) => {
    try {
      setLoading(true);
      setError(null);
      
      const data = await newsService.getPost(id);
      if (data) {
        setPost(data);
      } else {
        setError('<PERSON>hông tìm thấy bài viết');
      }
    } catch (err) {
      setError(err instanceof Error ? err.message : 'Có lỗi xảy ra khi tải bài viết');
      console.error('Error fetching post:', err);
    } finally {
      setLoading(false);
    }
  }, []);

  const fetchPostBySlug = useCallback(async (slug: string) => {
    try {
      setLoading(true);
      setError(null);
      
      const data = await newsService.getPostBySlug(slug);
      if (data) {
        setPost(data);
      } else {
        setError('Không tìm thấy bài viết');
      }
    } catch (err) {
      setError(err instanceof Error ? err.message : 'Có lỗi xảy ra khi tải bài viết');
      console.error('Error fetching post by slug:', err);
    } finally {
      setLoading(false);
    }
  }, []);

  const clearPost = useCallback(() => {
    setPost(null);
    setError(null);
  }, []);

  return {
    post,
    loading,
    error,
    fetchPost,
    fetchPostBySlug,
    clearPost
  };
}

export default useNewsDetail;