"use client";

interface MatchTeamsAndScoreProps {
  variant?: "default" | "compact" | "detailed";
  homeTeamName: string;
  awayTeamName: string;
  homeTeamLogo: string;
  awayTeamLogo: string;
  homeTeamScore: number;
  awayTeamScore: number;
}

export default function MatchTeamsAndScore({
  variant = "default",
  homeTeamName,
  awayTeamName,
  homeTeamLogo,
  awayTeamLogo,
  homeTeamScore,
  awayTeamScore,
}: MatchTeamsAndScoreProps) {
  if (variant === "compact") {
    return (
      <div className="flex items-center justify-between mb-2 lg:mb-3 relative z-10">
        {/* Home Team */}
        <div className="flex items-center gap-1 sm:gap-2 flex-1 min-w-0">
          <span className="text-xs sm:text-sm text-gray-900 dark:text-white font-medium truncate text-left w-[80px] sm:w-[100px] lg:w-[120px]">
            {homeTeamName}
          </span>
          <div className="w-6 h-6 sm:w-7 sm:h-7 lg:w-8 lg:h-8 rounded-full overflow-hidden bg-white border-2 border-gray-200 dark:border-custom-dark-secondary flex-shrink-0">
            <img
              src={homeTeamLogo}
              alt={homeTeamName}
              className="w-full h-full object-cover p-1"
            />
          </div>
        </div>

        {/* Score Display */}
        <div className="flex items-center gap-1 mx-1 lg:mx-2 flex-shrink-0">
          {homeTeamScore === 0 && awayTeamScore === 0 ? (
            <>
              <div className="bg-blue-600 text-white text-xs lg:text-sm px-1 sm:px-1.5 lg:px-2 py-0.5 sm:py-1 rounded font-medium min-w-[20px] text-center">
                0
              </div>
              <span className="text-gray-400 dark:text-custom-subtle text-sm sm:text-base lg:text-lg font-bold">
                :
              </span>
              <div className="bg-blue-600 text-white text-xs lg:text-sm px-1 sm:px-1.5 lg:px-2 py-0.5 sm:py-1 rounded font-medium min-w-[20px] text-center">
                0
              </div>
            </>
          ) : (
            <>
              <div className="bg-blue-600 text-white text-xs lg:text-sm px-1 sm:px-1.5 lg:px-2 py-0.5 sm:py-1 rounded font-medium min-w-[20px] text-center">
                {homeTeamScore}
              </div>
              <span className="text-gray-400 dark:text-custom-subtle text-sm sm:text-base lg:text-lg font-bold">
                :
              </span>
              <div className="bg-blue-600 text-white text-xs lg:text-sm px-1 sm:px-1.5 lg:px-2 py-0.5 sm:py-1 rounded font-medium min-w-[20px] text-center">
                {awayTeamScore}
              </div>
            </>
          )}
        </div>

        {/* Away Team */}
        <div className="flex items-center gap-1 sm:gap-2 flex-1 justify-end min-w-0">
          <div className="w-6 h-6 sm:w-7 sm:h-7 lg:w-8 lg:h-8 rounded-full overflow-hidden bg-white border-2 border-gray-200 dark:border-custom-dark-secondary flex-shrink-0">
            <img
              src={awayTeamLogo}
              alt={awayTeamName}
              className="w-full h-full object-cover p-1"
            />
          </div>
          <span className="text-xs sm:text-sm text-gray-900 dark:text-white font-medium truncate text-right w-[80px] sm:w-[100px] lg:w-[120px]">
            {awayTeamName}
          </span>
        </div>
      </div>
    );
  }

  // Detailed variant
  return (
    <div className="flex items-center justify-evenly gap-6 mb-2 lg:mb-3">
      {/* Home Team */}
      <div className="flex items-center gap-3 min-w-0">
        <span className="font-medium text-gray-900 dark:text-white truncate w-[120px] sm:w-[150px] lg:w-[200px]">
          {homeTeamName}
        </span>
        <div className="w-10 h-10 rounded-full overflow-hidden bg-white border-2 border-gray-200 dark:border-gray-600 flex-shrink-0">
          <img
            src={homeTeamLogo}
            alt={homeTeamName}
            className="w-full h-full object-cover p-1"
          />
        </div>
      </div>

      <div className="rounded-full border border-[#2563eb]/60 bg-[#2563eb]/60">
        {/* Score */}
        <div className="text-2xl font-bold text-gray-900 dark:text-white flex-shrink-0 min-w-[80px] text-center mx-4">
          <span className="">
            {homeTeamScore} - {awayTeamScore}
          </span>
        </div>
      </div>

      {/* Away Team */}
      <div className="flex items-center gap-3 min-w-0">
        <div className="w-10 h-10 rounded-full overflow-hidden bg-white border-2 border-gray-200 dark:border-gray-600 flex-shrink-0">
          <img
            src={awayTeamLogo}
            alt={awayTeamName}
            className="w-full h-full object-cover p-1"
          />
        </div>
        <span className="font-medium text-gray-900 dark:text-white truncate w-[120px] sm:w-[150px] lg:w-[200px]">
          {awayTeamName}
        </span>
      </div>
    </div>
  );
}