import { newsService } from "@/services/news.service";
import Image from "next/image";
import Link from "next/link";
import { notFound } from "next/navigation";
import type { Metadata } from "next";
import type { PostWithSeo } from "@/types/news.types";
import styles from "./news-content.module.css";

interface ArticlePageProps {
  params: Promise<{ slug: string }>;
}

// Generate metadata for SEO
export async function generateMetadata({ params }: ArticlePageProps): Promise<Metadata> {
  const resolvedParams = await params;
  const post = await newsService.getPostBySlug(resolvedParams.slug);

  if (!post) {
    return {
      title: "Không tìm thấy bài viết",
      description: "Bài viết bạn đang tìm kiếm không tồn tại hoặc đã bị xóa.",
    };
  }

  const title = post.meta_title || post.title;
  const description = post.meta_description || post.description || "Đ<PERSON>c bài viết mới nhất về bóng đá";
  const keywords = post.meta_keywords || "bóng đá, tin tức, thể thao";

  return {
    title,
    description,
    keywords,
    openGraph: {
      title,
      description,
      type: "article",
      publishedTime: post.published_at || undefined,
      authors: post.author?.full_name ? [post.author.full_name] : undefined,
      images: post.thumbnail ? [
        {
          url: post.thumbnail,
          width: 1200,
          height: 630,
          alt: post.title,
        }
      ] : undefined,
    },
    twitter: {
      card: "summary_large_image",
      title,
      description,
      images: post.thumbnail ? [post.thumbnail] : undefined,
    },
    alternates: {
      canonical: `/tin-tuc/${post.slug}`,
    },
  };
}

export default async function ArticlePage({ params }: ArticlePageProps) {
  const resolvedParams = await params;
  const post = await newsService.getPostBySlug(resolvedParams.slug);

  if (!post) {
    notFound();
  }

  // Format date helper
  const formatDate = (dateString: string) => {
    return new Date(dateString).toLocaleDateString('vi-VN', {
      day: '2-digit',
      month: '2-digit',
      year: 'numeric'
    });
  };

  return (
    <main className="min-h-screen bg-white dark:bg-custom-dark text-zinc-900 dark:text-white">
      <section className="border-b border-zinc-200 dark:border-gray-700 bg-white dark:bg-custom-dark">
        <div className="mx-auto max-w-4xl px-4 py-8">
          <h1 className="text-3xl font-bold leading-snug">{post.title}</h1>
          <div className="mt-2 text-sm text-zinc-600 dark:text-gray-400">
            {post.author?.full_name && `${post.author.full_name} • `}
            {post.published_at && formatDate(post.published_at)}
          </div>
          {post.description && (
            <p className="mt-4 text-lg text-zinc-700 dark:text-gray-300">{post.description}</p>
          )}
        </div>
      </section>

      <article className="mx-auto max-w-4xl px-4 py-6">
        {post.thumbnail && (
          <div className="relative mb-6 aspect-[16/9] overflow-hidden rounded-xl border border-zinc-200 dark:border-gray-700 bg-zinc-50 dark:bg-gray-700">
            <Image src={post.thumbnail} alt={post.title} fill className="object-cover" />
          </div>
        )}
        <div
          className={styles.newsContent}
          dangerouslySetInnerHTML={{ __html: post.content }}
        />
      </article>
    </main>
  );
}