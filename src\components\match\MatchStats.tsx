"use client";

import { MatchData, StatisticsData } from "@/types/match";
import { useEffect, useState } from "react";

import MatchBLVSection from "./MatchBLVSection";
import { UserInfo } from "@/services/userService";
import { fetchMatchStatistics } from "@/services/matchService";

interface MatchStatsProps {
  variant?: "default" | "compact" | "detailed";
  match: MatchData;
  blvInfo: UserInfo | null;
  blvLoading: boolean;
}

export default function MatchStats({
  variant = "default",
  match,
  blvInfo,
  blvLoading,
}: MatchStatsProps) {
  const [statistics, setStatistics] = useState<StatisticsData | null>(null);
  const [statsLoading, setStatsLoading] = useState(false);

  // Fetch statistics for the match
  useEffect(() => {
    const fetchStats = async () => {
      if (!match.id) return;

      setStatsLoading(true);
      try {
        const statsData = await fetchMatchStatistics(match.id);


        if (statsData && Array.isArray(statsData) && statsData.length > 0) {
          const fullMatchStats =
            statsData.find((stat) => stat.type === 0) || statsData[0];

          if (
            fullMatchStats &&
            fullMatchStats.stats &&
            fullMatchStats.stats.length >= 2
          ) {
            // Convert sang format StatisticsData
            const homeStats = fullMatchStats.stats[0];
            const awayStats = fullMatchStats.stats[1];

            const convertedStats: StatisticsData = {
              possession: {
                home: homeStats.ball_possession || 0,
                away: awayStats.ball_possession || 0,
              },
              shots: {
                home: homeStats.shots || 0,
                away: awayStats.shots || 0,
              },
              shotsOnTarget: {
                home: homeStats.shots_on_target || 0,
                away: awayStats.shots_on_target || 0,
              },
              corners: {
                home: homeStats.corner_kicks || 0,
                away: awayStats.corner_kicks || 0,
              },
              yellowCards: {
                home: homeStats.yellow_cards || 0,
                away: awayStats.yellow_cards || 0,
              },
              redCards: {
                home: homeStats.red_cards || 0,
                away: awayStats.red_cards || 0,
              },
              attacks: {
                home: homeStats.attacks || 0,
                away: awayStats.attacks || 0,
              },
              dangerousAttacks: {
                home: homeStats.dangerous_attack || 0,
                away: awayStats.dangerous_attack || 0,
              },
            };
            setStatistics(convertedStats);
          } else {
            setStatistics(null);
          }
        } else {
          setStatistics(null);
        }
      } catch (error) {
        // Failed to fetch statistics
        // Fallback to null when statistics conversion fails
        setStatistics(null);
      } finally {
        setStatsLoading(false);
      }
    };

    fetchStats();
  }, [match.id, match.statistics]);
  // Render statistics display
  const renderStatistics = () => {
    if (statsLoading) {
      return (
        <div className="text-xs text-gray-500 dark:text-gray-400 text-center">
          Đang tải thống kê...
        </div>
      );
    }

    if (!statistics) {
      return (
        <div className="text-xs text-gray-500 dark:text-gray-400 text-center">
          Chưa có thống kê
        </div>
      );
    }

    return (
      <div className="space-y-1">
        {/* Possession */}
        {statistics.possession && (
          <div className="flex items-center justify-between text-xs">
            <span className="text-gray-600 dark:text-gray-400">
              Kiểm soát bóng
            </span>
            <span className="text-gray-900 dark:text-white">
              {statistics.possession.home || 0}% -{" "}
              {statistics.possession.away || 0}%
            </span>
          </div>
        )}

        {statistics.shots && (
          <div className="flex items-center justify-between text-xs">
            <span className="text-gray-600 dark:text-gray-400">Cú sút</span>
            <span className="text-gray-900 dark:text-white">
              {(statistics.shots as { home?: number; away?: number }).home || 0} - {(statistics.shots as { home?: number; away?: number }).away || 0}
            </span>
          </div>
        )}

        {/* Shots on Target */}
        {statistics.shotsOnTarget && (
          <div className="flex items-center justify-between text-xs">
            <span className="text-gray-600 dark:text-gray-400">
              Sút trúng đích
            </span>
            <span className="text-gray-900 dark:text-white">
              {statistics.shotsOnTarget.home || 0} -{" "}
              {statistics.shotsOnTarget.away || 0}
            </span>
          </div>
        )}

        {/* Corners */}
        {statistics.corners && (
          <div className="flex items-center justify-between text-xs">
            <span className="text-gray-600 dark:text-gray-400">Phạt góc</span>
            <span className="text-gray-900 dark:text-white">
              {statistics.corners.home || 0} - {statistics.corners.away || 0}
            </span>
          </div>
        )}

        {/* Attacks */}
        {statistics.attacks && (
          <div className="flex items-center justify-between text-xs">
            <span className="text-gray-600 dark:text-gray-400">Tấn công</span>
            <span className="text-gray-900 dark:text-white">
              {statistics.attacks.home || 0} - {statistics.attacks.away || 0}
            </span>
          </div>
        )}

        {/* Dangerous Attacks */}
        {statistics.dangerousAttacks && (
          <div className="flex items-center justify-between text-xs">
            <span className="text-gray-600 dark:text-gray-400">
              Tấn công nguy hiểm
            </span>
            <span className="text-gray-900 dark:text-white">
              {statistics.dangerousAttacks.home || 0} -{" "}
              {statistics.dangerousAttacks.away || 0}
            </span>
          </div>
        )}
      </div>
    );
  };

  if (variant === "detailed") {
    return (
      <div className="grid grid-cols-10 relative z-10 mt-auto">
        <div className="col-span-7 overflow-x-hidden">
          <MatchBLVSection
            blvId={match.liveData?.[0]?.blv}
            blvInfo={blvInfo}
            blvLoading={blvLoading}
          />
        </div>
        <div className="col-span-3 gap-1 sm:gap-1.5">
          {/* Vendor Logo */}
          <div className="w-12 h-10 sm:w-16 sm:h-12 rounded flex items-center justify-center">
            <img
              src="/vendor/ok-logo.png"
              alt="OK Logo"
              className="w-full h-full object-contain"
            />
          </div>
        </div>
      </div>
    );
  }

  // Compact variant
  return (
    <div className="space-y-2 relative z-10">
      {/* Cards and Statistics Row */}
      <div className="flex items-center justify-between">
        <div className="flex items-center gap-1.5 lg:gap-2">
          {/* Cards Display */}
          <div className="flex items-center gap-2">
            {/* Home Team Cards */}
            <div className="flex items-center gap-1">
              <span className="text-xs text-gray-600 dark:text-gray-400">
                H:
              </span>
              <div className="flex gap-1">
                {Array.from({ length: match.cards?.redHome || 0 }).map(
                  (_, index) => (
                    <div
                      key={`red-home-${index}`}
                      className="w-4 h-4 bg-red-500 rounded-sm border-2 border-red-700 shadow-md"
                    ></div>
                  )
                )}
              </div>
              <div className="flex gap-1">
                {Array.from({ length: match.cards?.yellowHome || 0 }).map(
                  (_, index) => (
                    <div
                      key={`yellow-home-${index}`}
                      className="w-4 h-4 bg-yellow-400 rounded-sm border-2 border-yellow-600 shadow-md"
                    ></div>
                  )
                )}
              </div>
            </div>

            {/* Away Team Cards */}
            <div className="flex items-center gap-1">
              <span className="text-xs text-gray-600 dark:text-gray-400">
                K:
              </span>
              <div className="flex gap-1">
                {Array.from({ length: match.cards?.yellowAway || 0 }).map(
                  (_, index) => (
                    <div
                      key={`yellow-away-${index}`}
                      className="w-4 h-4 bg-yellow-400 rounded-sm border-2 border-yellow-600 shadow-md"
                    ></div>
                  )
                )}
              </div>
              <div className="flex gap-1">
                {Array.from({ length: match.cards?.redAway || 0 }).map(
                  (_, index) => (
                    <div
                      key={`red-away-${index}`}
                      className="w-4 h-4 bg-red-500 rounded-sm border-2 border-red-700 shadow-md"
                    ></div>
                  )
                )}
              </div>
            </div>
          </div>
        </div>

        <div className="flex items-center gap-1.5 lg:gap-2">
          {/* Vendor Logo */}
          <div className="w-12 h-10 sm:w-16 sm:h-12 rounded flex items-center justify-center">
            <img
              src="/vendor/ok-logo.png"
              alt="OK Logo"
              className="w-full h-full object-contain"
            />
          </div>
        </div>
      </div>

      {/* Statistics Row for Compact */}
      <div className="px-2 py-1 bg-white/30 dark:bg-gray-800/30 rounded">
        {renderStatistics()}
      </div>
    </div>
  );
}