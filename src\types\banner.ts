interface BannerExpert {
  id: string;
  name: string;
  role: string;
  email: string;
  avatar_url: string | null;
}

export interface BannerData {
  id: string;
  expert: BannerExpert;
  tournament: string;
  team_name: string;
  closing_bet: string;
  saying: string;
  status: string | null;
  created_at: string;
  updated_at: string;
  created_by: string | null;
  sent_at: string | null;
  type: string;
  order?: string;
  displayed?: string;
  winDisplayed?: string;
  updatedAt?: string;
}
