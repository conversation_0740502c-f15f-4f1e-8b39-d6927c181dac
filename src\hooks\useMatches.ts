import { useState, useCallback, useEffect, useMemo } from 'react';
import { MatchData } from '@/types/match';
import { FILTER_TYPES, type FilterType } from '@/constants/filters';
import {
  fetchMatches,
  fetchAllMatches,
  getAllCount
} from '@/services/matchService';

interface UseMatchesReturn {
  matches: MatchData[];
  loading: boolean;
  loadingMore: boolean;
  error: string | null;
  hasMore: boolean;
  currentPage: number;
  currentFilter: FilterType;
  filterCounts: Record<FilterType, number>;
  fetchMatchesByFilter: (filter: FilterType, category?: string) => Promise<void>;
  fetchAllCounts: (category?: string) => Promise<void>;
  loadMore: () => Promise<void>;
  clearError: () => void;
}

export const useMatches = (): UseMatchesReturn => {
  const [matches, setMatches] = useState<MatchData[]>([]);
  const [loading, setLoading] = useState(false);
  const [loadingMore, setLoadingMore] = useState(false);
  const [error, setError] = useState<string | null>(null);
  const [hasMore, setHasMore] = useState(true);
  const [currentPage, setCurrentPage] = useState(1);
  const [currentFilter, setCurrentFilter] = useState<FilterType>(FILTER_TYPES.ALL);
  const [currentCategory, setCurrentCategory] = useState('football');
  const [filterCounts, setFilterCounts] = useState<Record<FilterType, number>>({
    [FILTER_TYPES.ALL]: 0,
    [FILTER_TYPES.LIVE]: 0,
    [FILTER_TYPES.HOT]: 0,
    [FILTER_TYPES.TODAY]: 0,
    [FILTER_TYPES.TOMORROW]: 0
  });

  const [isInitialized, setIsInitialized] = useState(false);

  // Memoized helper functions
  const getTodayDateString = useMemo(() => {
    const today = new Date();
    const year = today.getFullYear();
    const month = String(today.getMonth() + 1).padStart(2, '0');
    const day = String(today.getDate()).padStart(2, '0');
    return `${year}${month}${day}`;
  }, []);

  const getTomorrowDateString = useMemo(() => {
    const tomorrow = new Date();
    tomorrow.setDate(tomorrow.getDate() + 1);
    const year = tomorrow.getFullYear();
    const month = String(tomorrow.getMonth() + 1).padStart(2, '0');
    const day = String(tomorrow.getDate()).padStart(2, '0');
    return `${year}${month}${day}`;
  }, []);

  const updateFilterCounts = useCallback(async (category: string) => {
    try {
      const countsResponse = await getAllCount(category);

      const newCounts = {
        [FILTER_TYPES.ALL]: countsResponse.all || 0,
        [FILTER_TYPES.LIVE]: countsResponse.live || 0,
        [FILTER_TYPES.HOT]: countsResponse.hot || 0,
        [FILTER_TYPES.TODAY]: countsResponse.today || 0,
        [FILTER_TYPES.TOMORROW]: countsResponse.tomorrow || 0
      };

      setFilterCounts(newCounts);
    } catch (err) {
    }
  }, []);

  const clearError = useCallback(() => {
    setError(null);
  }, []);

  const fetchAllCounts = useCallback(async (category: string = 'football') => {
    const countsResponse = await getAllCount(category);
    setFilterCounts(countsResponse);
  }, []);

  const fetchMatchesByFilter = useCallback(async (filter: FilterType, category: string = 'football') => {

    const isRefresh = filter === currentFilter && category === currentCategory;

    if (isRefresh && matches.length > 0) {
      return;
    }

    setLoading(true);
    setError(null);
    setCurrentPage(1);
    setCurrentFilter(filter);

    try {
      let result: MatchData[];
      let total = 0;

      switch (filter) {
        case FILTER_TYPES.LIVE:
          const liveResult = await fetchMatches({
            category,
            status: 'live',
            limit: 9,
            offset: 0,
            sortBy: 'status,date',
            sortOrder: 'DESC,ASC'
          });
          result = liveResult.data;
          total = liveResult.total;
          break;
        case FILTER_TYPES.HOT:
          const hotResult = await fetchMatches({
            category,
            typeMatch: 'hot',
            limit: 9,
            offset: 0,
            sortBy: 'status,time,date',
            sortOrder: 'DESC,ASC,ASC'
          });
          result = hotResult.data;
          total = hotResult.total;
          break;
        case FILTER_TYPES.TODAY:
          const todayResult = await fetchMatches({
            category,
            date: getTodayDateString,
            limit: 9,
            offset: 0,
            sortBy: 'status,time,date',
            sortOrder: 'DESC,ASC,ASC'
          });
          result = todayResult.data;
          total = todayResult.total;
          break;
        case FILTER_TYPES.TOMORROW:
          const tomorrowResult = await fetchMatches({
            category,
            date: getTomorrowDateString,
            limit: 9,
            offset: 0,
            sortBy: 'status,time,date',
            sortOrder: 'DESC,ASC,ASC'
          });
          result = tomorrowResult.data;
          total = tomorrowResult.total;
          break;
        default:
          const fetchResult = await fetchMatches({
            category,
            limit: 100,
            offset: 0,
            sortBy: 'status,time,date',
            sortOrder: 'DESC,ASC,ASC'
          });
          result = fetchResult.data;
          total = fetchResult.total;
      }

      if (!result || !Array.isArray(result)) {
        setError('Invalid data format received from server');
        return;
      }

      setMatches(result);
      setHasMore(total >= 9);

      if (category !== currentCategory) {
        await updateFilterCounts(category);
        setCurrentCategory(category);
      }
    } catch (err) {
      const errorMessage = err instanceof Error ? err.message : 'Failed to fetch matches';
      setError(errorMessage);
    } finally {
      setLoading(false);
    }
  }, [currentCategory, currentFilter, getTodayDateString, getTomorrowDateString, updateFilterCounts]);

  const loadMore = useCallback(async () => {
    if (loadingMore || !hasMore || loading) return;

    setLoadingMore(true);
    const nextPage = currentPage + 1;
    const offset = (nextPage - 1) * 9;

    try {
      let additionalMatches: MatchData[];

      switch (currentFilter) {
        case FILTER_TYPES.LIVE:
          const liveResult = await fetchMatches({
            category: currentCategory,
            status: 'live',
            limit: 9,
            offset,
            sortBy: 'status,date',
            sortOrder: 'DESC,ASC'
          });
          additionalMatches = liveResult.data;
          break;
        case FILTER_TYPES.HOT:
          const hotResult = await fetchMatches({
            category: currentCategory,
            typeMatch: 'hot',
            limit: 9,
            offset,
            sortBy: 'status,time,date',
            sortOrder: 'DESC,ASC,ASC'
          });
          additionalMatches = hotResult.data;
          break;
        case FILTER_TYPES.TODAY:
          const todayResult = await fetchMatches({
            category: currentCategory,
            date: getTodayDateString,
            limit: 9,
            offset,
            sortBy: 'status,time,date',
            sortOrder: 'DESC,ASC,ASC'
          });
          additionalMatches = todayResult.data;
          break;
        case FILTER_TYPES.TOMORROW:
          const tomorrowResult = await fetchMatches({
            category: currentCategory,
            date: getTomorrowDateString,
            limit: 9,
            offset,
            sortOrder: 'DESC,ASC,ASC'
          });
          additionalMatches = tomorrowResult.data;
          break;
        default:
          const defaultResult = await fetchMatches({
            category: currentCategory,
            limit: 9,
            offset,
            sortBy: 'status,time,date',
            sortOrder: 'DESC,ASC,ASC'
          });
          additionalMatches = defaultResult.data;
      }

      if (additionalMatches.length > 0) {
        setMatches(prev => {
          const prevMatches = Array.isArray(prev) ? prev : [];
          const newMatches = [...prevMatches, ...additionalMatches];
          return newMatches;
        });

        setCurrentPage(nextPage);
        setHasMore(additionalMatches.length === 9);
      } else {
        setHasMore(false);
      }
    } catch (err) {
      const errorMessage = err instanceof Error ? err.message : 'Failed to load more matches';
      setError(errorMessage);
      // Error loading more matches
    } finally {
      setLoadingMore(false);
    }
  }, [currentFilter, currentCategory, currentPage, hasMore, loadingMore, loading, getTodayDateString, getTomorrowDateString]);

  return {
    matches,
    loading,
    loadingMore,
    error,
    hasMore,
    currentPage,
    currentFilter,
    filterCounts,
    fetchMatchesByFilter,
    fetchAllCounts,
    loadMore,
    clearError,
  };
};
