"use client";

import { TabIndex, isValidTabIndex } from "@/constants/tabs";
import { detectMobile } from "@/lib/utils/deviceDetection";
import { useEffect, useState } from "react";

export interface UseTabNavigationReturn {
  activeTab: number;
  selectedRoomId: string | null;
  systemMessageSent: boolean;
  handleTabChange: (tabIndex: number, roomId?: string) => void;
  setActiveTab: (tab: number) => void;
  setSelectedRoomId: (roomId: string | null) => void;
  resetSystemMessage: () => void;
}

/**
 * Hook for managing tab navigation and related state
 */
export const useTabNavigation = (): UseTabNavigationReturn => {
  const [activeTab, setActiveTab] = useState<number>(TabIndex.CHAT);
  const [selectedRoomId, setSelectedRoomId] = useState<string | null>(null);
  const [systemMessageSent, setSystemMessageSent] = useState(false);
  const [isMobile, setIsMobile] = useState(false);

  // Update mobile state
  useEffect(() => {
    const updateMobileState = () => {
      setIsMobile(detectMobile());
    };

    updateMobileState();
    window.addEventListener("resize", updateMobileState);

    return () => {
      window.removeEventListener("resize", updateMobileState);
    };
  }, []);

  // Handle tab change with validation
  const handleTabChange = (tabIndex: number, roomId?: string) => {
    // Validate tab index
    if (!isValidTabIndex(tabIndex)) {
      console.warn(`Invalid tab index: ${tabIndex}`);
      return;
    }

    setActiveTab(tabIndex);

    if (roomId) {
      setSelectedRoomId(roomId);
    }
  };

  // System message logic for chat tab
  useEffect(() => {
    if (activeTab === TabIndex.CHAT && !systemMessageSent) {
      const timer = setTimeout(() => {
        // Create system message object
        const systemMessage = {
          id: `system-${Date.now()}`,
          content:
            "Quà tặng tân thủ lên tới 10 triệu đồng trong tháng. Đặc biệt Megalive hàng tuần quà tặng lên tới 288 triệu.",
          created_at: new Date().toISOString(),
          user_id: "system",
          user: {
            full_name: "Tin nhắn hệ thống",
            avatar_url: "/icon/social.svg",
          },
        };

        console.log("Adding system message to chat:", systemMessage);
        setSystemMessageSent(true);
      }, 3 * 60 * 1000); // 3 minutes

      return () => clearTimeout(timer);
    }
  }, [activeTab, systemMessageSent]);

  // Reset system message when switching away from chat tab
  useEffect(() => {
    if (activeTab !== TabIndex.CHAT) {
      setSystemMessageSent(false);
    }
  }, [activeTab]);

  const resetSystemMessage = () => {
    setSystemMessageSent(false);
  };

  return {
    activeTab,
    selectedRoomId,
    systemMessageSent,
    handleTabChange,
    setActiveTab,
    setSelectedRoomId,
    resetSystemMessage,
  };
};
