"use client";

import { useEffect, useRef, useState } from "react";
import Artplayer from "artplayer";
import Hls from "hls.js";

type HlsClass = {
  new (): {
    loadSource(src: string): void;
    attachMedia(video: HTMLVideoElement): void;
    destroy(): void;
  };
  isSupported(): boolean;
};

type ArtInstance = {
  switchUrl: (url: string) => void;
  destroy?: () => void;
  video?: HTMLVideoElement;
};

interface VideoPlayerProps {
  videoUrl: string;
  autoPlay?: boolean;
  muted?: boolean;
  volume?: number;
  theme?: string;
  className?: string;
  showControls?: boolean;
  isJoin?: boolean;
  onJoinClick?: () => void;
  breakOutContainer?: boolean; // New prop to break out of container
}

interface ArtPlayerOptions {
  container: HTMLElement;
  url: string;
  autoplay: boolean;
  volume: number;
  muted: boolean;
  theme: string;
  fullscreen: boolean;
  fullscreenWeb: boolean;
  pip: boolean;
  autoMini: boolean;
  lock: boolean;
  lang: string;
  disableContextMenu: boolean;
  controls: Array<{
    position: string;
    index: number;
    html: string;
    click: () => void;
  }>;
  customType: {
    m3u8: (video: HTMLVideoElement, url: string) => void;
  };
}

const VideoPlayer: React.FC<VideoPlayerProps> = ({
  videoUrl = "https://test-streams.mux.dev/x36xhzz/x36xhzz.m3u8",
  className = "",
  autoPlay = false,
  muted = true,
  volume = 0.7,
  theme = "#0f1214",
  showControls = true,
  isJoin = false,
  onJoinClick,
  breakOutContainer = false,
}) => {
  const [ready, setReady] = useState(false);
  const playerRef = useRef<HTMLDivElement | null>(null);
  const artInstanceRef = useRef<ArtInstance | null>(null);

  useEffect(() => {
    let destroyed = false;

    // Disable PIP mode globally
    async function setup() {
      try {
        if (!playerRef.current || destroyed) return;

        const art = new Artplayer({
          container: playerRef.current,
          url: videoUrl,
          autoplay: autoPlay,
          volume: volume,
          muted: muted,
          theme: theme,
          isLive: true,
          fullscreen: true,
          fullscreenWeb: false,
          pip: true,
          autoMini: false,
          lock: true,
          lang: "vi",
          controls: [
            {
              position: "left",
              index: 30,
              html: '<div style="display: flex; align-items: center; padding: 0 8px; color: #ff4444; font-weight: bold; font-size: 12px;"><span style="margin-right: 4px; animation: bubble 1.5s ease-in-out infinite;">●</span>LIVE</div><style>@keyframes bubble { 0%, 100% { transform: scale(1); } 50% { transform: scale(1.3); } }</style>',
              click: () => {},
            },
          ],
          customType: {
            m3u8: function (video: HTMLVideoElement, url: string) {
              if (Hls.isSupported()) {
                const hls = new Hls();
                hls.loadSource(url);
                hls.attachMedia(video);
              } else if (video.canPlayType("application/vnd.apple.mpegurl")) {
                video.src = url;
              } else {
                // HLS not supported in this browser
              }
            },
          },
        });

        // If src ends with .m3u8, hint ArtPlayer
        if (videoUrl.endsWith(".m3u8")) {
          art.switchUrl(videoUrl);
        }

        artInstanceRef.current = art;
        setReady(true);
      } catch (e) {
        // ArtPlayer setup error
        // Fallback: set ready to true even if there's an error
        setReady(true);
      }
    }
    setup();

    // Fallback: set ready to true after a timeout if setup fails
    const timeoutId = setTimeout(() => {
      setReady(true);
    }, 3000);

    return () => {
      destroyed = true;
      clearTimeout(timeoutId);
      artInstanceRef.current?.destroy?.();
    };
  }, [videoUrl, autoPlay, muted, volume, theme]);

  return (
    <div
      className={`relative overflow-hidden bg-black ${className} ${
        breakOutContainer ? "video-mobile-breakout" : ""
      }`}
    >
      <div className="aspect-video" ref={playerRef} />

      {/* Loading overlay */}
      {!ready && (
        <div className="pointer-events-none absolute inset-0 flex items-center justify-center">
          <div className="rounded-lg bg-black/60 px-3 py-2 text-sm text-white">
            Đang khởi tạo player…
          </div>
        </div>
      )}

      {/* Join overlay */}
      {isJoin && (
        <div className="absolute inset-0 bg-black/50 flex items-center justify-center z-10">
          <button
            onClick={onJoinClick}
            className="border-2 border-blue-600 bg-transparent hover:bg-blue-600 text-blue-600 hover:text-white font-medium px-4 py-2 rounded-full shadow-lg transition-all duration-200 transform hover:scale-105 text-sm"
          >
            VÀO PHÒNG LIVE
          </button>
        </div>
      )}
    </div>
  );
};

export default VideoPlayer;
