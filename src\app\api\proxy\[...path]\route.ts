import { NextRequest, NextResponse } from 'next/server';

export async function GET(request: NextRequest, { params }: { params: Promise<{ path: string[] }> }) {
  try {
    const { path } = await params;
    const searchParams = request.nextUrl.searchParams;
    
    // Construct the target URL
    const targetUrl = new URL(`https://www.ngoaihangtv.xyz/api/${path.join('/')}`);
    
    // Copy search parameters
    searchParams.forEach((value, key) => {
      targetUrl.searchParams.append(key, value);
    });
    // Make the request to the target server
    const response = await fetch(targetUrl.toString(), {
      method: 'GET',
      headers: {
        'Accept': 'application/json',
        'Content-Type': 'application/json',
        'User-Agent': 'Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36',
      },
    });

    if (!response.ok) {
      console.error('Proxy request failed:', response.status, response.statusText);
      return NextResponse.json(
        { error: `Proxy request failed: ${response.status} ${response.statusText}` },
        { status: response.status }
      );
    }

    const data = await response.json();
    
    return NextResponse.json(data);
  } catch (error) {
    console.error('Proxy error:', error);
    return NextResponse.json(
      { error: 'Internal proxy error' },
      { status: 500 }
    );
  }
}

export async function POST(request: NextRequest, { params }: { params: Promise<{ path: string[] }> }) {
  try {
    const { path } = await params;
    const body = await request.text();
    
    // Construct the target URL
    const targetUrl = `https://www.ngoaihangtv.xyz/api/${path.join('/')}`;
    

    // Make the request to the target server
    const response = await fetch(targetUrl, {
      method: 'POST',
      headers: {
        'Accept': 'application/json',
        'Content-Type': 'application/json',
        'User-Agent': 'Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36',
      },
      body: body,
    });

    if (!response.ok) {
      console.error('Proxy POST request failed:', response.status, response.statusText);
      return NextResponse.json(
        { error: `Proxy request failed: ${response.status} ${response.statusText}` },
        { status: response.status }
      );
    }

    const data = await response.json();
    
    return NextResponse.json(data);
  } catch (error) {
    console.error('Proxy POST error:', error);
    return NextResponse.json(
      { error: 'Internal proxy error' },
      { status: 500 }
    );
  }
}