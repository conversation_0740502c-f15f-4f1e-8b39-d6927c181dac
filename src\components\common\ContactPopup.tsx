"use client";

import { useState, useEffect } from "react";

import Image from "next/image";

interface ContactPopupProps {
  isOpen: boolean;
  onClose: () => void;
}

export default function ContactPopup({ isOpen, onClose }: ContactPopupProps) {
  // Handle ESC key press
  useEffect(() => {
    const handleEscKey = (event: KeyboardEvent) => {
      if (event.key === "Escape") {
        onClose();
      }
    };

    if (isOpen) {
      document.addEventListener("keydown", handleEscKey);
    }

    return () => {
      document.removeEventListener("keydown", handleEscKey);
    };
  }, [isOpen, onClose]);

  if (!isOpen) return null;

  const handleZaloClick = () => {
    window.open("https://zalo.me/0994190627", "_blank");
    onClose();
  };

  const handleTelegramClick = () => {
    window.open("https://t.me/thuphuongepl", "_blank");
    onClose();
  };

  return (
    <div
      className="fixed inset-0 bg-black/50 flex items-center justify-center z-50 p-4"
      onClick={onClose}
    >
      <div
        className="bg-white dark:bg-gray-800 rounded-2xl p-6 max-w-sm w-full shadow-xl"
        onClick={(e) => e.stopPropagation()}
      >
        <div className="text-center">
          <h3 className="text-lg font-bold text-gray-900 dark:text-white mb-2">
            Liên hệ nhận ưu đãi
          </h3>
          <p className="text-sm text-gray-600 dark:text-gray-300 mb-6">
            Chọn phương thức liên hệ để nhận ưu đãi
          </p>

          <div className="space-y-3">
            {/* Zalo Button */}
            <button
              onClick={handleZaloClick}
              className="w-full flex items-center justify-center gap-3 bg-blue-500 hover:bg-blue-600 text-white px-4 py-3 rounded-lg transition-colors"
            >
              <Image
                src="/icon/zalo.png"
                alt="corner_kick"
                width={24}
                height={24}
              />
              <span className="font-medium">Zalo</span>
            </button>

            {/* Telegram Button */}
            <button
              onClick={handleTelegramClick}
              className="w-full flex items-center justify-center gap-3 bg-blue-400 hover:bg-blue-500 text-white px-4 py-3 rounded-lg transition-colors"
            >
              <Image
                src="/icon/telegram.png"
                alt="corner_kick"
                width={24}
                height={24}
              />
              <span className="font-medium">Telegram</span>
            </button>
          </div>

          {/* Close Button */}
          <button
            onClick={onClose}
            className="mt-4 text-sm text-gray-500 dark:text-gray-400 hover:text-gray-700 dark:hover:text-gray-200 transition-colors"
          >
            Đóng
          </button>
        </div>
      </div>
    </div>
  );
}
