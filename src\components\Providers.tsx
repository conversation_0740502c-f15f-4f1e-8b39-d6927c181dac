"use client";

import { useState, useEffect } from "react";
import { ThemeProvider } from "./ThemeProvider";
import { ScreenLockProvider } from "./ScreenLockContext";
import { AuthProvider } from "@/contexts/AuthContext";
import { SupabaseProvider } from "@/contexts/SupabaseContext";
import LoadingScreen from "./LoadingScreen";

export default function Providers({ children }: { children: React.ReactNode }) {
  const [isLoading, setIsLoading] = useState(true);
  const [isClient, setIsClient] = useState(false);

  useEffect(() => {
    setIsClient(true);
    // Simulate app loading time
    const timer = setTimeout(() => {
      setIsLoading(false);
    }, 1000); // 1 giây loading

    return () => clearTimeout(timer);
  }, []);

  // Đảm bảo server và client render giống nhau
  if (!isClient) {
    return (
      <SupabaseProvider>
        <ThemeProvider>
          <ScreenLockProvider>
            <AuthProvider>
              {children}
            </AuthProvider>
          </ScreenLockProvider>
        </ThemeProvider>
      </SupabaseProvider>
    );
  }

  if (isLoading) {
    return (
      <LoadingScreen 
        onLoadingComplete={() => setIsLoading(false)}
        minLoadingTime={1000}
      />
    );
  }

  return (
    <SupabaseProvider>
      <ThemeProvider>
        <ScreenLockProvider>
          <AuthProvider>
            {children}
          </AuthProvider>
        </ScreenLockProvider>
      </ThemeProvider>
    </SupabaseProvider>
  );
}