"use client";

import { useEffect, useState } from 'react';
import Link from 'next/link';
import { useRouter } from 'next/navigation';

export default function NotFound() {
  const [count, setCount] = useState(5);
  const router = useRouter();

  useEffect(() => {
    const timer = setInterval(() => {
      setCount((prev) => {
        if (prev <= 1) {
          clearInterval(timer);
          router.push('/');
          return 0;
        }
        return prev - 1;
      });
    }, 1000);

    return () => clearInterval(timer);
  }, [router]);

  return (
    <div className="flex flex-col items-center justify-center min-h-screen bg-gray-50 p-4 text-center">
      <h1 
        className="text-9xl font-bold text-blue-600 mb-8"
        style={{
          textShadow: '5px 5px 0 #0d47a1, 10px 10px 15px rgba(0,0,0,0.2)',
          transform: 'perspective(500px) rotateX(20deg)',
          transition: 'all 0.3s ease',
          lineHeight: 1,
        }}
      >
        404
      </h1>
      <div className="text-2xl text-gray-700 max-w-2xl mb-8">
        Rất tiếc! Không tìm thấy trang :(<br />
        Liên kết này có thể bị hỏng, hoặc trang này có thể đã được loại bỏ.
      </div>
      <div className="text-xl text-blue-600 font-semibold">
        Bạn sẽ chuyển hướng về trang chủ sau {count} giây
      </div>
      <Link 
        href="/" 
        className="mt-8 px-6 py-3 bg-blue-600 text-white rounded-lg hover:bg-blue-700 transition-colors"
      >
        Về trang chủ ngay
      </Link>
    </div>
  );
}
