import "./globals.css";

import type { Metadata, Viewport } from "next";
import Script from "next/script";

import Footer from "@/components/Footer";
import Header from "@/components/Header";
import Providers from "@/components/Providers";
import { TooltipProvider } from "@/components/ui/tooltip";
import ConditionalMenuBottom from "@/components/layout/ConditionalMenuBottom";
import { Toaster } from 'sonner'

export const metadata: Metadata = {
  title: "NGOAIHANG TV - Trực tiếp bóng đá Ngoại Hạng TV, xem TTBD online HD",
  description:
    "NGOAIHANG TV trực tiếp bóng đá hôm nay chất lượng cao, xem bóng đá online miễn phí tốc độ nhanh, bình luận hấp dẫn các giải Ngoạ<PERSON> hạng Anh, La Liga, Serie A.",
  manifest: "/manifest.webmanifest",
  icons: {
    icon: "/web-app-manifest-192x192.png",
    apple: "/web-app-manifest-192x192.png",
  },
  // PWA specific metadata
  appleWebApp: {
    capable: true,
    statusBarStyle: "default",
    title: "NGOAIHANG TV",
  },
  formatDetection: {
    telephone: false,
  },
  openGraph: {
    type: "website",
    locale: "vi_VN",
    url: "https://ngoaihangtv.live",
    title: "NGOAIHANG TV - Trực tiếp bóng đá online",
    description: "Xem trực tiếp bóng đá hôm nay chất lượng cao, miễn phí",
    siteName: "NGOAIHANG TV",
  },
  twitter: {
    card: "summary_large_image",
    title: "NGOAIHANG TV - Trực tiếp bóng đá online",
    description: "Xem trực tiếp bóng đá hôm nay chất lượng cao, miễn phí",
  },
};

export const viewport: Viewport = {
  width: "device-width",
  initialScale: 1,
  maximumScale: 1,
  themeColor: "#0f1214",
};

export default function RootLayout({
  children,
}: {
  children: React.ReactNode;
}) {
  return (
    <html lang="vi" className="h-full">
      <body className="min-h-full antialiased bg-white dark:bg-custom-dark transition-colors duration-200">
        {/* Google tag (gtag.js) */}
        <Script
          src="https://www.googletagmanager.com/gtag/js?id=G-R67FVZ6XZ1"
          strategy="afterInteractive"
        />
        <Script id="google-analytics" strategy="afterInteractive">
          {`
            window.dataLayer = window.dataLayer || [];
            function gtag(){dataLayer.push(arguments);}
            gtag('js', new Date());
            gtag('config', 'G-R67FVZ6XZ1');
          `}
        </Script>

        <Providers>
          <Header />
          <main className="min-h-screen">
            <div className="max-w-[1600px] mx-auto">
              <div className="container-content">
                <TooltipProvider delayDuration={100}>
                  {children}
                  <Toaster position="top-right" richColors closeButton theme="dark" />
                  <ConditionalMenuBottom />
                </TooltipProvider>
              </div>
            </div>
          </main>
          <Footer />
        </Providers>
      </body>
    </html>
  );
}
