"use client";

import Image from "next/image";
import { useState, useEffect, useCallback } from "react";

interface LoadingScreenProps {
  onLoadingComplete: () => void;
  minLoadingTime?: number;
}

export default function LoadingScreen({ onLoadingComplete, minLoadingTime = 2000 }: LoadingScreenProps) {
  const [isVisible, setIsVisible] = useState(true);
  const [isClient, setIsClient] = useState(false);

  const completeLoading = useCallback(() => {
    setIsVisible(false);
    setTimeout(() => {
      onLoadingComplete();
    }, 500);
  }, [onLoadingComplete]);

  useEffect(() => {
    setIsClient(true);
    const timer = setTimeout(() => {
      completeLoading();
    }, minLoadingTime);

    return () => clearTimeout(timer);
  }, [minLoadingTime, completeLoading]);

  // Đảm bảo server và client render giống nhau
  if (!isClient || !isVisible) {
    return null;
  }

  return (
    <div 
      className="fixed inset-0 z-50 bg-white flex items-center justify-center"
      suppressHydrationWarning
    >
      {/* Main Content */}
      <div className="relative z-10 flex flex-col items-center space-y-6 animate-fade-in-up">
        {/* Logo */}
        <div className="relative">
          <Image
            src="/ngoaihangtv.png"
            alt="NgoaiHangTV"
            width={200}
            height={100}
            className="w-40 h-auto"
            priority
            unoptimized
            suppressHydrationWarning
          />
        </div>
      </div>
    </div>
  );
}
