"use client";

import { StatisticsData } from "@/types/match";
import { useState } from "react";

type MatchStats = StatisticsData;

interface MatchStatsPopupProps {
  isVisible: boolean;
  onClose: () => void;
  matchStats: MatchStats[];
  homeTeamName: string;
  awayTeamName: string;
  homeTeamId: string;
  awayTeamId: string;
  leagueName: string;
  matchStatus: string;
  position?: { x: number; y: number };
  onMouseEnter?: () => void;
  onMouseLeave?: () => void;
}

export default function MatchStatsPopup({
  isVisible,
  onClose,
  matchStats,
  homeTeamName,
  awayTeamName,
  homeTeamId,
  awayTeamId,
  leagueName,
  matchStatus,
  position,
  onMouseEnter,
  onMouseLeave,
}: MatchStatsPopupProps) {
  if (!isVisible || !matchStats || matchStats.length === 0) return null;

  const stats = matchStats[0];

  if (!stats) {
    return (
      <div
        className="fixed z-50 flex items-center justify-center bg-black/50"
        onClick={onClose}
      >
        <div
          className="relative bg-gray-800/95 backdrop-blur-sm rounded-lg p-4 w-full max-w-md mx-4"
          onClick={(e) => e.stopPropagation()}
          style={
            position
              ? {
                  left: position.x,
                  top: position.y,
                  transform: "translate(-50%, 0%)",
                }
              : {}
          }
        >
          <div className="text-white text-center">
            <h3 className="font-medium mb-2">{leagueName}</h3>
            <p className="text-sm text-gray-300 mb-4">
              {homeTeamName} vs {awayTeamName}
            </p>
            <p className="text-xs text-gray-400">
              Không có dữ liệu thống kê chi tiết
            </p>
            <p className="text-xs text-gray-400 mt-2">Status: {matchStatus}</p>
          </div>
          <button
            title="Close"
            onClick={onClose}
            className="absolute top-2 right-2 text-gray-400 hover:text-white transition-colors"
          >
            <svg
              className="w-5 h-5"
              fill="none"
              stroke="currentColor"
              viewBox="0 0 24 24"
            >
              <path
                strokeLinecap="round"
                strokeLinejoin="round"
                strokeWidth={2}
                d="M6 18L18 6M6 6l12 12"
              />
            </svg>
          </button>
        </div>
      </div>
    );
  }

  const getStatValue = (value: number, isPercentage = false) => {
    if (isPercentage) {
      return `${value}%`;
    }
    return value.toString();
  };

  const getStatBarWidth = (
    homeValue: number,
    awayValue: number,
    isPercentage = false
  ) => {
    if (isPercentage) {
      const total = homeValue + awayValue;
      return total > 0 ? (homeValue / total) * 100 : 50;
    }
    const total = homeValue + awayValue;
    return total > 0 ? (homeValue / total) * 100 : 50;
  };

  const statsData = [
    {
      label: "Tỷ lệ kiểm soát bóng",
      homeValue: stats.possession?.home || 0,
      awayValue: stats.possession?.away || 0,
      isPercentage: true,
    },
    {
      label: "Phạt góc",
      homeValue: stats.corners?.home || 0,
      awayValue: stats.corners?.away || 0,
      isPercentage: false,
    },
    {
      label: "Bàn thắng",
      homeValue: stats.goals?.home || 0,
      awayValue: stats.goals?.away || 0,
      isPercentage: false,
    },
    {
      label: "Thẻ",
      homeValue: stats.cards?.home || 0,
      awayValue: stats.cards?.away || 0,
      isPercentage: false,
    },
    {
      label: "Sút bóng",
      homeValue: stats.shots?.home || 0,
      awayValue: stats.shots?.away || 0,
      isPercentage: false,
    },
    {
      label: "Tấn công",
      homeValue: stats.attacks?.home || 0,
      awayValue: stats.attacks?.away || 0,
      isPercentage: false,
    },
    {
      label: "Chuyền bóng",
      homeValue: stats.passes?.home || 0,
      awayValue: stats.passes?.away || 0,
      isPercentage: false,
    },
  ];

  return (
    <div
      className="fixed inset-0 z-50 flex items-center justify-center bg-black/50"
      onClick={onClose}
    >
      <div
        className="relative bg-gray-800/95 backdrop-blur-sm rounded-lg p-4 w-full max-w-md mx-4"
        onClick={(e) => e.stopPropagation()}
        onMouseEnter={onMouseEnter}
        onMouseLeave={onMouseLeave}
        style={
          position
            ? {
                position: "absolute",
                left: position.x,
                top: position.y,
                transform: "translate(-50%, 0%)",
              }
            : {}
        }
      >
        {/* Header */}
        <div className="flex items-center justify-between mb-4">
          <h3 className="text-white font-medium text-sm">{leagueName}</h3>
          <div className="bg-orange-500 text-white text-xs px-2 py-1 rounded-full">
            {matchStatus === "live" ? "LIVE" : matchStatus.toUpperCase()}
          </div>
        </div>

        {/* Match Info */}
        <div className="text-center mb-4">
          <p className="text-sm text-gray-300">
            {homeTeamName} vs {awayTeamName}
          </p>
        </div>

        {/* Statistics */}
        <div className="space-y-3 max-h-64 overflow-y-auto">
          {statsData.map((stat, index) => (
            <div key={index} className="space-y-2">
              {/* Values Row */}
              <div className="flex items-center justify-between text-xs">
                <span className="text-white font-medium w-16 text-right">
                  {getStatValue(stat.homeValue, stat.isPercentage)}
                </span>
                <span className="text-gray-300 text-center flex-1 px-2">
                  {stat.label}
                </span>
                <span className="text-white font-medium w-16 text-left">
                  {getStatValue(stat.awayValue, stat.isPercentage)}
                </span>
              </div>

              {/* Progress Bar */}
              <div className="w-full bg-gray-700 rounded-full h-2">
                <div
                  className="bg-green-500 h-2 rounded-full transition-all duration-300"
                  style={{
                    width: `${getStatBarWidth(
                      stat.homeValue,
                      stat.awayValue,
                      stat.isPercentage
                    )}%`,
                  }}
                />
              </div>
            </div>
          ))}
        </div>

        {/* Close Button */}
        <button
          title="Close"
          onClick={onClose}
          className="absolute top-2 right-2 text-gray-400 hover:text-white transition-colors"
        >
          <svg
            className="w-5 h-5"
            fill="none"
            stroke="currentColor"
            viewBox="0 0 24 24"
          >
            <path
              strokeLinecap="round"
              strokeLinejoin="round"
              strokeWidth={2}
              d="M6 18L18 6M6 6l12 12"
            />
          </svg>
        </button>
      </div>
    </div>
  );
}