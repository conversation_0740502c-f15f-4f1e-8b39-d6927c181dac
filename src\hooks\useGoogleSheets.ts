import { useState, useCallback } from 'react';
import { SheetData } from '@/services/googleSheetsService';

interface UseGoogleSheetsReturn {
  data: SheetData | null;
  loading: boolean;
  error: string | null;
  fetchSheetData: (spreadsheetId: string, range?: string) => Promise<void>;
  fetchSheetByName: (spreadsheetId: string, sheetName: string, startCell?: string, endCell?: string) => Promise<void>;
  fetchMultipleRanges: (spreadsheetId: string, ranges: string[]) => Promise<void>;
  updateSheetCell: (spreadsheetId: string, sheetName: string, order: string | number, column: string, value: string | number | boolean) => Promise<void>;
  clearData: () => void;
}

export const useGoogleSheets = (): UseGoogleSheetsReturn => {
  const [data, setData] = useState<SheetData | null>(null);
  const [loading, setLoading] = useState<boolean>(false);
  const [error, setError] = useState<string | null>(null);

  const fetchSheetData = useCallback(async (spreadsheetId: string, range?: string) => {
    setLoading(true);
    setError(null);

    try {
      const url = range
        ? `/api/google-sheets?spreadsheetId=${encodeURIComponent(spreadsheetId)}&range=${encodeURIComponent(range)}`
        : `/api/google-sheets?spreadsheetId=${encodeURIComponent(spreadsheetId)}`;

      const response = await fetch(url);
      const result = await response.json();
      if (result.success) {
        setData(result.data);
      } else {
        setError(result.error || 'Failed to fetch data');
      }
    } catch (err) {
      setError(err instanceof Error ? err.message : 'An error occurred');
    } finally {
      setLoading(false);
    }
  }, []);

  const fetchSheetByName = useCallback(async (spreadsheetId: string, sheetName: string, startCell: string = 'A1', endCell: string = 'H') => {
    setLoading(true);
    setError(null);

    try {
      const url = `/api/google-sheets?spreadsheetId=${encodeURIComponent(spreadsheetId)}&sheet=${encodeURIComponent(sheetName)}`;
      const response = await fetch(url);
      const result = await response.json();

      if (result.success) {
        setData(result.data);
      } else {
        setError(result.error || 'Failed to fetch data');
      }
    } catch (err) {
      setError(err instanceof Error ? err.message : 'An error occurred');
    } finally {
      setLoading(false);
    }
  }, []);

  const fetchMultipleRanges = useCallback(async (spreadsheetId: string, ranges: string[]) => {
    setLoading(true);
    setError(null);

    try {
      const response = await fetch('/api/google-sheets', {
        method: 'POST',
        headers: {
          'Content-Type': 'application/json',
        },
        body: JSON.stringify({ spreadsheetId, ranges }),
      });

      const result = await response.json();

      if (result.success) {
        setData(result.data[0] || null);
      } else {
        setError(result.error || 'Failed to fetch data');
      }
    } catch (err) {
      setError(err instanceof Error ? err.message : 'An error occurred');
    } finally {
      setLoading(false);
    }
  }, []);

  const updateSheetCell = useCallback(async (
    spreadsheetId: string,
    sheetName: string,
    order: string | number,
    column: string,
    value: string | number | boolean
  ) => {
    setLoading(true);
    setError(null);

    try {
      const response = await fetch('/api/google-sheets/update', {
        method: 'POST',
        headers: {
          'Content-Type': 'application/json',
        },
        body: JSON.stringify({
          spreadsheetId,
          sheetName,
          order,
          column,
          value,
        }),
      });

      const result = await response.json();

      if (!result.success) {
        setError(result.error || 'Failed to update cell');
      }
    } catch (err) {
      setError(err instanceof Error ? err.message : 'An error occurred while updating cell');
    } finally {
      setLoading(false);
    }
  }, []);

  const clearData = useCallback(() => {
    setData(null);
    setError(null);
  }, []);

  return {
    data,
    loading,
    error,
    fetchSheetData,
    fetchSheetByName,
    fetchMultipleRanges,
    updateSheetCell,
    clearData,
  };
};
