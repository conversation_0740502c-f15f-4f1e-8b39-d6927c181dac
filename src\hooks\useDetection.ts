import { useState, useEffect } from "react";
import {
  getDeviceInfo,
  supportsVisualViewport,
  type DeviceInfo,
} from "@/lib/utils/deviceDetection";

export interface UseDetectionReturn extends DeviceInfo {
  supportsVisualViewport: boolean;
  windowWidth: number;
  windowHeight: number;
}

/**
 * Hook để detect thông tin thiết bị và kích thước màn hình
 * @returns Thông tin thiết bị và kích thước màn hình
 */
export const useDetection = (): UseDetectionReturn => {
  const [deviceInfo, setDeviceInfo] = useState<DeviceInfo>({
    isMobile: false,
    isIOS: false,
    isAndroid: false,
    isTablet: false,
  });

  const [windowSize, setWindowSize] = useState({
    width: 0,
    height: 0,
  });

  const [visualViewportSupport, setVisualViewportSupport] = useState(false);

  useEffect(() => {
    // Khởi tạo thông tin thiết bị
    const updateDeviceInfo = () => {
      setDeviceInfo(getDeviceInfo());
      setVisualViewportSupport(supportsVisualViewport());
      setWindowSize({
        width: window.innerWidth,
        height: window.innerHeight,
      });
    };

    // Gọi lần đầu
    updateDeviceInfo();

    // Lắng nghe sự kiện resize
    const handleResize = () => {
      setDeviceInfo(getDeviceInfo());
      setWindowSize({
        width: window.innerWidth,
        height: window.innerHeight,
      });
    };

    window.addEventListener("resize", handleResize);

    // Cleanup
    return () => {
      console.log("Removing resize event listener");
      window.removeEventListener("resize", handleResize);
    };
  }, []);

  return {
    ...deviceInfo,
    supportsVisualViewport: visualViewportSupport,
    windowWidth: windowSize.width,
    windowHeight: windowSize.height,
  };
};

export default useDetection;
