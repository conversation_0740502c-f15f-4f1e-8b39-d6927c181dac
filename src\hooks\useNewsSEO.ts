import { useMemo } from 'react';
import type { PostWithSeo } from '@/types/news.types';

export interface SEOData {
  title: string;
  description: string;
  keywords?: string;
  ogTitle?: string;
  ogDescription?: string;
  ogImage?: string;
  canonicalUrl?: string;
}

export interface UseNewsSEOReturn {
  seoData: SEOData;
  metaTags: Record<string, string>;
}

interface UseNewsSEOProps {
  post?: PostWithSeo | null;
  defaultTitle?: string;
  defaultDescription?: string;
  baseUrl?: string;
}

export function useNewsSEO({
  post,
  defaultTitle = 'Tin tức - NgoaiHang TV',
  defaultDescription = 'Cập nhật tin nóng, phân tích và nhận định mới nhất về bóng đá',
  baseUrl = 'https://ngoaihangtv.live'
}: UseNewsSEOProps): UseNewsSEOReturn {
  
  const seoData = useMemo((): SEOData => {
    if (!post) {
      return {
        title: defaultTitle,
        description: defaultDescription
      };
    }

    const title = post.meta_title || post.title;
    const description = post.meta_description || post.description || defaultDescription;
    const keywords = post.meta_keywords || undefined;
    const ogImage = post.thumbnail || undefined;
    const canonicalUrl = post.slug ? `${baseUrl}/tin-tuc/${post.slug}` : undefined;

    return {
      title: `${title} - NgoaiHang TV`,
      description,
      keywords,
      ogTitle: title,
      ogDescription: description,
      ogImage,
      canonicalUrl
    };
  }, [post, defaultTitle, defaultDescription, baseUrl]);

  const metaTags = useMemo(() => {
    const tags: Record<string, string> = {
      'title': seoData.title,
      'description': seoData.description,
      'og:type': 'article',
      'twitter:card': 'summary_large_image'
    };

    // Keywords
    if (seoData.keywords) {
      tags['keywords'] = seoData.keywords;
    }

    // Open Graph tags
    if (seoData.ogTitle) {
      tags['og:title'] = seoData.ogTitle;
    }

    if (seoData.ogDescription) {
      tags['og:description'] = seoData.ogDescription;
    }

    if (seoData.ogImage) {
      tags['og:image'] = seoData.ogImage;
    }

    // Canonical URL
    if (seoData.canonicalUrl) {
      tags['canonical'] = seoData.canonicalUrl;
    }

    // Twitter Card tags
    if (seoData.ogTitle) {
      tags['twitter:title'] = seoData.ogTitle;
    }

    if (seoData.ogDescription) {
      tags['twitter:description'] = seoData.ogDescription;
    }

    if (seoData.ogImage) {
      tags['twitter:image'] = seoData.ogImage;
    }

    return tags;
  }, [seoData]);

  return {
    seoData,
    metaTags
  };
}

export default useNewsSEO;