"use client";

import { useSupabase } from "@/contexts/SupabaseContext";
import { useState, useEffect, useRef, useCallback } from "react";

interface UserPresence {
  user_id: string;
  is_online: boolean;
  last_seen: string;
  profiles?: {
    full_name?: string;
    email: string;
    avatar_url?: string;
  };
}

interface UseOnlineStatusProps {
  currentUserId: string;
  channelId?: string;
}

export function useOnlineStatus({
  currentUserId,
  channelId,
}: UseOnlineStatusProps) {
  const { supabase } = useSupabase();
  const [onlineUsers, setOnlineUsers] = useState<UserPresence[]>([]);
  const [isCurrentUserOnline, setIsCurrentUserOnline] = useState(true);
  const heartbeatInterval = useRef<NodeJS.Timeout | null>(null);
  const beforeUnloadHandler = useRef<(() => void) | null>(null);

  // Set user online when hook initializes
  const setUserOnline = useCallback(async () => {
    try {
      await supabase.rpc("update_user_presence", {
        user_uuid: currentUserId,
        online_status: true,
      });
      setIsCurrentUserOnline(true);
    } catch (error) {
      // Error setting user online
    }
  }, [currentUserId, supabase]);

  // Set user offline
  const setUserOffline = useCallback(async () => {
    try {
      await supabase.rpc("update_user_presence", {
        user_uuid: currentUserId,
        online_status: false,
      });
      setIsCurrentUserOnline(false);
    } catch (error) {
      // Error setting user offline
    }
  }, [currentUserId, supabase]);

  // Load online users
  const loadOnlineUsers = useCallback(async () => {
    try {
      let query = supabase
        .from("user_presence")
        .select(
          `
          *,
          profiles:user_id (
            full_name,
            email,
            avatar_url
          )
        `
        )
        .eq("is_online", true);

      // If channelId is provided, filter by channel members
      if (channelId) {
        const { data: memberIds } = await supabase
          .from("chat_room_members")
          .select("user_id")
          .eq("chat_room_id", channelId);

        if (memberIds && memberIds.length > 0) {
          const userIds = memberIds.map((member) => member.user_id);
          query = query.in("user_id", userIds);
        }
      }

      const { data, error } = await query;

      if (error) throw error;
      setOnlineUsers(data || []);
    } catch (error) {
      // Error loading online users
    }
  }, [channelId, supabase]);

  // Subscribe to presence changes
  const subscribeToPresence = useCallback(() => {
    const channel = supabase
      .channel("user_presence_changes")
      .on(
        "postgres_changes",
        {
          event: "*",
          schema: "public",
          table: "user_presence",
        },
        async (payload) => {
          if (
            payload.eventType === "INSERT" ||
            payload.eventType === "UPDATE"
          ) {
            // Get user profile for the presence update
            const { data: profile } = await supabase
              .from("profiles")
              .select("full_name, email, avatar_url")
              .eq("id", payload.new.user_id)
              .single();

            const presenceUpdate = {
              ...payload.new,
              profiles: profile,
            } as UserPresence;

            setOnlineUsers((prev) => {
              const filtered = prev.filter(
                (user) => user.user_id !== payload.new.user_id
              );
              if (payload.new.is_online) {
                return [...filtered, presenceUpdate];
              }
              return filtered;
            });
          } else if (payload.eventType === "DELETE") {
            setOnlineUsers((prev) =>
              prev.filter((user) => user.user_id !== payload.old.user_id)
            );
          }
        }
      )
      .subscribe();

    return () => {
      supabase.removeChannel(channel);
    };
  }, [supabase]);

  // Setup heartbeat to maintain online status
  const setupHeartbeat = useCallback(() => {
    heartbeatInterval.current = setInterval(async () => {
      if (isCurrentUserOnline) {
        await supabase.rpc("update_user_presence", {
          user_uuid: currentUserId,
          online_status: true,
        });
      }
    }, 30000); // Update every 30 seconds
  }, [isCurrentUserOnline, currentUserId, supabase]);

  // Setup beforeunload handler
  const setupBeforeUnloadHandler = useCallback(() => {
    beforeUnloadHandler.current = () => {
      // Use sendBeacon for reliable offline status update
      if (navigator.sendBeacon) {
        const formData = new FormData();
        formData.append("user_id", currentUserId);
        formData.append("online_status", "false");
        navigator.sendBeacon("/api/presence", formData);
      }
    };

    window.addEventListener("beforeunload", beforeUnloadHandler.current);
  }, [currentUserId]);

  // Get user online status
  const getUserOnlineStatus = (userId: string): boolean => {
    return onlineUsers.some(
      (user) => user.user_id === userId && user.is_online
    );
  };

  // Get last seen time
  const getLastSeen = (userId: string): string | null => {
    const user = onlineUsers.find((user) => user.user_id === userId);
    return user?.last_seen || null;
  };

  // Format last seen time
  const formatLastSeen = (lastSeen: string): string => {
    const now = new Date();
    const lastSeenDate = new Date(lastSeen);
    const diffInMinutes = Math.floor(
      (now.getTime() - lastSeenDate.getTime()) / (1000 * 60)
    );

    if (diffInMinutes < 1) return "Vừa xong";
    if (diffInMinutes < 60) return `${diffInMinutes} phút trước`;
    if (diffInMinutes < 1440)
      return `${Math.floor(diffInMinutes / 60)} giờ trước`;
    return `${Math.floor(diffInMinutes / 1440)} ngày trước`;
  };

  useEffect(() => {
    setUserOnline();
    loadOnlineUsers();
    const unsubscribe = subscribeToPresence();
    setupHeartbeat();
    setupBeforeUnloadHandler();

    return () => {
      if (heartbeatInterval.current) {
        clearInterval(heartbeatInterval.current);
      }
      if (beforeUnloadHandler.current) {
        window.removeEventListener("beforeunload", beforeUnloadHandler.current);
      }
      setUserOffline();
      unsubscribe();
    };
  }, [
    currentUserId,
    channelId,
    loadOnlineUsers,
    setUserOffline,
    setUserOnline,
    setupBeforeUnloadHandler,
    setupHeartbeat,
    subscribeToPresence,
  ]);

  return {
    onlineUsers,
    isCurrentUserOnline,
    getUserOnlineStatus,
    getLastSeen,
    formatLastSeen,
    setUserOnline,
    setUserOffline,
  };
}
