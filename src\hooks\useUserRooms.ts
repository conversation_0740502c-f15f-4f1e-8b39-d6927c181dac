import { useSupabase } from "@/contexts/SupabaseContext";
import { ChatService } from "@/services/chat.service";
import { ChatRoom } from "@/types";
import { useCallback, useEffect, useState } from "react";

export const useUserRooms = (userId: string | null) => {
  const [loading, setLoading] = useState(false);
  const [userRooms, setUserRooms] = useState<ChatRoom[]>([]);
  const { supabase } = useSupabase();

  const getUserRooms = useCallback(
    async (userId: string) => {
      setLoading(true);
      const chatService = new ChatService(supabase);
      const { rooms, error } = await chatService.getUserChatRooms(userId);
      if (error) {
        console.error("Error fetching rooms:", error);
        return;
      }
      setUserRooms(rooms);
      setLoading(false);
    },
    [supabase]
  );

  useEffect(() => {
    if (userId) {
      getUserRooms(userId);
    }
  }, [getUserRooms, userId]);

  return {
    userRooms,
    loading,
  };
};
