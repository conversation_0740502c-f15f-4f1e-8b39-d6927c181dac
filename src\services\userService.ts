export interface UserInfo {
  uid: string;
  displayName: string;
  email?: string;
  photoURL?: string;
  [key: string]: unknown;
}

interface CacheData {
  data: UserInfo[];
  expiry: number;
}

interface UserCacheData {
  user: UserInfo;
  expiry: number;
}

const SESSION_STORAGE_KEYS = {
  USER_CACHE: 'qclg_user_cache',
  USER_EXPIRY: 'qclg_user_expiry',
  INDIVIDUAL_USERS: 'qclg_individual_users',
  ALL_USERS_BY_ROLE: 'qclg_all_users_by_role'
};

const CACHE_DURATION = 5 * 60 * 1000; // 5 phút

// eslint-disable-next-line @typescript-eslint/no-explicit-any
const pendingRequests = new Map<string, Promise<any>>();

let globalUsersByRole: Record<string, UserInfo[]> | null = null;
let globalCacheExpiry: number = 0;

const getFromSessionStorage = <T>(key: string): T | null => {
  if (typeof window === 'undefined') return null;
  try {
    const item = sessionStorage.getItem(key);
    return item ? JSON.parse(item) : null;
  } catch {
    return null;
  }
};

const setToSessionStorage = <T>(key: string, value: T): void => {
  if (typeof window === 'undefined') return;
  try {
    sessionStorage.setItem(key, JSON.stringify(value));
  } catch {
    // Session storage full or unavailable
  }
};

const removeFromSessionStorage = (key: string): void => {
  if (typeof window === 'undefined') return;
  try {
    sessionStorage.removeItem(key);
  } catch {
    // Session storage unavailable
  }
};

export const getAllUsers = async (role: string, limit: number): Promise<UserInfo[] | null> => {
  const cacheKey = `${role}-${limit}`;
  const now = Date.now();
  
  const cachedData = getFromSessionStorage<Record<string, CacheData>>(SESSION_STORAGE_KEYS.USER_CACHE);
  if (cachedData && cachedData[cacheKey] && now < cachedData[cacheKey].expiry) {
    return cachedData[cacheKey].data;
  }

  if (pendingRequests.has(cacheKey)) {
    return await pendingRequests.get(cacheKey);
  }

  const url = `/api/proxy/forum/search-users?&role=${role}&limit=${limit}`;
  
  const requestPromise = (async () => {
    try {
      const response = await fetch(url);

      if (!response.ok) {
        throw new Error(`HTTP error! status: ${response.status}`);
      }

      const userData = await response.json();
      let users: UserInfo[];

      if (Array.isArray(userData)) {
        users = userData;
      } else {
        users = userData.users || userData;
      }

      const currentCache = cachedData || {};
      currentCache[cacheKey] = {
        data: users,
        expiry: now + CACHE_DURATION
      };
      setToSessionStorage(SESSION_STORAGE_KEYS.USER_CACHE, currentCache);

      return users;
    } catch (error) {
      // Failed to get all users
      return null;
    } finally {
      pendingRequests.delete(cacheKey);
    }
  })();

  pendingRequests.set(cacheKey, requestPromise);
  
  return await requestPromise;
};

let isLoadingAllUsers = false;

const loadAllUsersByRoles = async (): Promise<Record<string, UserInfo[]>> => {
  const now = Date.now();
  
  if (globalUsersByRole && now < globalCacheExpiry) {
    return globalUsersByRole;
  }
  
  const cachedAllUsers = getFromSessionStorage<{data: Record<string, UserInfo[]>, expiry: number}>(SESSION_STORAGE_KEYS.ALL_USERS_BY_ROLE);
  if (cachedAllUsers && now < cachedAllUsers.expiry) {
    globalUsersByRole = cachedAllUsers.data;
    globalCacheExpiry = cachedAllUsers.expiry;
    return globalUsersByRole;
  }
  
  if (isLoadingAllUsers) {
    while (isLoadingAllUsers) {
      await new Promise(resolve => setTimeout(resolve, 50));
    }
    if (globalUsersByRole && now < globalCacheExpiry) {
      return globalUsersByRole;
    }
  }
  
  const allUsersKey = 'all-users-by-roles';
  if (pendingRequests.has(allUsersKey)) {
    return await pendingRequests.get(allUsersKey);
  }
  
  isLoadingAllUsers = true;
  
  const requestPromise = (async () => {
    try {
      const roles = ["admin", "commentator"];
      const userPromises = roles.map(role => getAllUsers(role, 1000));
      const allRoleUsers = await Promise.all(userPromises);
      
      const result: Record<string, UserInfo[]> = {};
      roles.forEach((role, index) => {
        result[role] = allRoleUsers[index] || [];
      });
      
      globalUsersByRole = result;
      globalCacheExpiry = now + CACHE_DURATION;
      
      setToSessionStorage(SESSION_STORAGE_KEYS.ALL_USERS_BY_ROLE, {
        data: result,
        expiry: globalCacheExpiry
      });
      
      return result;
    } catch (error) {
      console.error('Error loading all users by roles:', error);
      return {};
    } finally {
      isLoadingAllUsers = false;
      pendingRequests.delete(allUsersKey);
    }
  })();
  
  pendingRequests.set(allUsersKey, requestPromise);
  return await requestPromise;
};

const getAllUsersByRole = async (role: string): Promise<UserInfo[]> => {
  const allUsers = await loadAllUsersByRoles();
  return allUsers[role] || [];
};

const findUserInRoleLists = async (blvId: string): Promise<UserInfo | null> => {
  const cachedUser = getCachedUser(blvId);
  if (cachedUser) {
    return cachedUser;
  }

  const roles = ["admin", "commentator"];
  
  const userPromises = roles.map(role => getAllUsersByRole(role));
  
  try {
    const allRoleUsers = await Promise.all(userPromises);
    
    for (let i = 0; i < roles.length; i++) {
      const users = allRoleUsers[i];
      const foundUser = users.find((user: UserInfo) => user.uid === blvId);
      
      if (foundUser) {
        setCachedUser(blvId, foundUser);
        return foundUser;
      }
    }
    
    const adminUsers = allRoleUsers[0];
    const defaultUser = (adminUsers && adminUsers[0]) || null;
    
    if (defaultUser) {
      setCachedUser(blvId, defaultUser);
    }
    
    return defaultUser;
    
  } catch (error) {
    
    try {
      const adminUsers = await getAllUsersByRole("admin");
      const fallbackUser = (adminUsers && adminUsers[0]) || null;
      
      if (fallbackUser) {
        setCachedUser(blvId, fallbackUser);
      }
      
      return fallbackUser;
    } catch (fallbackError) {
      return null;
    }
  }
};

export const clearUserCache = () => {
  removeFromSessionStorage(SESSION_STORAGE_KEYS.USER_CACHE);
  removeFromSessionStorage(SESSION_STORAGE_KEYS.INDIVIDUAL_USERS);
};

export const invalidateRoleCache = (role: string) => {
  const cachedData = getFromSessionStorage<Record<string, CacheData>>(SESSION_STORAGE_KEYS.USER_CACHE);
  if (cachedData) {
    const keysToDelete = Object.keys(cachedData).filter(key => 
      key.startsWith(`${role}-`) || key === `all-${role}`
    );
    keysToDelete.forEach(key => {
      delete cachedData[key];
    });
    setToSessionStorage(SESSION_STORAGE_KEYS.USER_CACHE, cachedData);
  }
};

const getCachedUser = (userId: string): UserInfo | null => {
  const cachedUsers = getFromSessionStorage<Record<string, UserCacheData>>(SESSION_STORAGE_KEYS.INDIVIDUAL_USERS);
  if (cachedUsers && cachedUsers[userId]) {
    const userData = cachedUsers[userId];
    if (Date.now() < userData.expiry) {
      return userData.user;
    }
  }
  return null;
};

const setCachedUser = (userId: string, user: UserInfo): void => {
  const cachedUsers = getFromSessionStorage<Record<string, UserCacheData>>(SESSION_STORAGE_KEYS.INDIVIDUAL_USERS) || {};
  cachedUsers[userId] = {
    user,
    expiry: Date.now() + CACHE_DURATION
  };
  setToSessionStorage(SESSION_STORAGE_KEYS.INDIVIDUAL_USERS, cachedUsers);
};

export const getUserInfo = findUserInRoleLists;