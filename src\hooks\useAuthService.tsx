"use client";

import { useEffect, useState } from "react";
import { useSupabase } from "@/contexts/SupabaseContext";
import type { AuthProfile, LoginCredentials, RegisterData } from "@/types";
import type { User } from "@supabase/supabase-js";
import { AuthService } from "@/services/auth.service";

export function useAuthService() {
  const { supabase } = useSupabase();
  const [user, setUser] = useState<User | null>(null);
  const [profile, setProfile] = useState<AuthProfile | null>(null);
  const [loading, setLoading] = useState(true);

  useEffect(() => {
    const initializeAuth = async () => {
      try {
        const authService = new AuthService(supabase);
        const { user, error } = await authService.getCurrentUser();
        if (error) {
          // Error getting current user
          setUser(null);
          setProfile(null);
          return;
        }

        setUser(user);

        if (user) {
          const { profile, error: profileError } = await authService.getProfile(
            user.id
          );
          if (profileError) {
            // Error getting profile
            setProfile(null);
            return;
          }
          setProfile(profile);
        } else {
          setProfile(null);
        }
      } catch (error) {
        // Error initializing auth
        setUser(null);
        setProfile(null);
      } finally {
        setLoading(false);
      }
    };

    initializeAuth();
    const authService = new AuthService(supabase);
    const {
      data: { subscription },
    } = authService.onAuthStateChange(async (event, session) => {
      setUser(session?.user ?? null);

      if (session?.user) {
        const { profile, error } = await authService.getProfile(
          session.user.id
        );
        if (error) {
          // Error getting profile on auth change
          return;
        }
        setProfile(profile);
      } else {
        setProfile(null);
      }

      setLoading(false);
    });

    return () => subscription.unsubscribe();
  }, []);

  const signIn = async (credentials: LoginCredentials) => {
    const authService = new AuthService(supabase);
    const { user, error } = await authService.signIn(credentials);
    return { user, error };
  };

  const signUp = async (registerData: RegisterData) => {
    const authService = new AuthService(supabase);
    const { user, error } = await authService.signUp(registerData);
    return { user, error };
  };

  const signInWithGoogle = async () => {
    const authService = new AuthService(supabase);
    const { error } = await authService.signInWithGoogle();
    return { error };
  };

  const signOut = async () => {
    const authService = new AuthService(supabase);
    const { error } = await authService.signOut();
    return { error };
  };

  const updateProfile = async (
    userId: string,
    updates: Partial<AuthProfile>
  ) => {
    const authService = new AuthService(supabase);
    const { profile, error } = await authService.updateProfile(userId, updates);
    if (!error && profile) {
      setProfile(profile);
    }
    return { profile, error };
  };

  return {
    user,
    profile,
    loading,
    signIn,
    signUp,
    signInWithGoogle,
    signOut,
    updateProfile,
  };
}
