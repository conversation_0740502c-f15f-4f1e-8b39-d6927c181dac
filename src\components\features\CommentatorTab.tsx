"use client";

import { useState, useEffect } from 'react';

interface CommentatorTabProps {
  isLoading?: boolean;
}

export default function CommentatorTab({ isLoading = false }: CommentatorTabProps) {
  // Loading skeleton component
  const LoadingSkeleton = () => (
    <div className="h-full space-y-6 p-6">
      {/* Commentator Info Skeleton */}
      <div className="bg-white dark:bg-custom-dark rounded-lg p-6 border border-gray-200 dark:border-gray-700">
        <div className="h-6 bg-gray-200 dark:bg-gray-700 rounded-lg mb-4 animate-pulse"></div>
        <div className="text-center py-8">
          <div className="w-16 h-16 bg-gray-200 dark:bg-gray-700 rounded-full mx-auto mb-4 animate-pulse"></div>
          <div className="space-y-2">
            <div className="w-32 h-4 bg-gray-200 dark:bg-gray-700 rounded mx-auto animate-pulse"></div>
            <div className="w-48 h-3 bg-gray-200 dark:bg-gray-700 rounded mx-auto animate-pulse"></div>
          </div>
        </div>
      </div>

      {/* Live Commentary Skeleton */}
      <div className="bg-white dark:bg-custom-dark rounded-lg p-6 border border-gray-200 dark:border-gray-700">
        <div className="h-6 bg-gray-200 dark:bg-gray-700 rounded-lg mb-4 animate-pulse"></div>
        <div className="space-y-3">
          {Array.from({ length: 4 }).map((_, index) => (
            <div key={index} className="flex items-start gap-3">
              <div className="w-8 h-8 bg-gray-200 dark:bg-gray-700 rounded-full flex-shrink-0 animate-pulse"></div>
              <div className="flex-1 space-y-2">
                <div className="w-24 h-3 bg-gray-200 dark:bg-gray-700 rounded animate-pulse"></div>
                <div className="w-full h-3 bg-gray-200 dark:bg-gray-700 rounded animate-pulse"></div>
                <div className="w-3/4 h-3 bg-gray-200 dark:bg-gray-700 rounded animate-pulse"></div>
              </div>
            </div>
          ))}
        </div>
      </div>

      {/* Match Events Skeleton */}
      <div className="bg-white dark:bg-custom-dark rounded-lg p-6 border border-gray-200 dark:border-gray-700">
        <div className="h-6 bg-gray-200 dark:bg-gray-700 rounded-lg mb-4 animate-pulse"></div>
        <div className="space-y-3">
          {Array.from({ length: 3 }).map((_, index) => (
            <div key={index} className="flex items-center gap-3">
              <div className="w-3 h-3 bg-gray-200 dark:bg-gray-700 rounded-full flex-shrink-0 animate-pulse"></div>
              <div className="w-20 h-3 bg-gray-200 dark:bg-gray-700 rounded animate-pulse"></div>
              <div className="flex-1 h-3 bg-gray-200 dark:bg-gray-700 rounded animate-pulse"></div>
              <div className="w-16 h-3 bg-gray-200 dark:bg-gray-700 rounded animate-pulse"></div>
            </div>
          ))}
        </div>
      </div>
    </div>
  );

  // Show skeleton when loading
  if (isLoading) {
    return <LoadingSkeleton />;
  }

  return (
    <div className="h-full space-y-6 p-6">
      {/* Commentator Info */}
    </div>
  );
} 