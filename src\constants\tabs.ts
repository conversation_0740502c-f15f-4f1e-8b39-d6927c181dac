/**
 * Tab configuration constants
 */

export interface TabItem {
  index: number;
  label: string;
  icon: string;
  iconActive: string;
}

export const TAB_INDICES = {
  HOME: 0,
  CHAT: 1,
  PRIVATE_CHAT: 2,
  STATS: 3,
  COMMENTATOR: 4,
  LINEUP: 5,
  ODDS: 6,
  HEAD_TO_HEAD: 7,
  INFO: 8,
  RELATED_MATCHES: 9,
} as const;

/**
 * Tab indices enum for better type safety
 */
export enum TabIndex {
  HOME = 0,
  CHAT = 1,
  PRIVATE_CHAT = 2,
  STATS = 3,
  COMMENTATOR = 4,
  LINEUP = 5,
  ODDS = 6,
  HEAD_TO_HEAD = 7,
  INFO = 8,
  RELATED_MATCHES = 9,
}

/**
 * Desktop tabs configuration (without "Trận đấu liên quan")
 */
export const DESKTOP_TABS: TabItem[] = [
  {
    index: TAB_INDICES.HOME,
    label: "Home",
    icon: "/icon/home.svg",
    iconActive: "/icon/home.svg",
  },
  {
    index: TAB_INDICES.CHAT,
    label: "Chat",
    icon: "/icon/chat.svg",
    iconActive: "/icon/chat-active.svg",
  },
  {
    index: TAB_INDICES.PRIVATE_CHAT,
    label: "Chat riêng",
    icon: "/icon/chat.svg",
    iconActive: "/icon/chat-active.svg",
  },
  {
    index: TAB_INDICES.STATS,
    label: "Thông Số",
    icon: "/icon/thong-so.svg",
    iconActive: "/icon/thong-so-active.svg",
  },
  // Commented out tabs - can be enabled later
  // {
  //   index: TAB_INDICES.COMMENTATOR,
  //   label: "Bình luận viên",
  //   icon: "/icon/dien-bien.svg",
  //   iconActive: "/icon/dien-bien-active.svg"
  // },
  // {
  //   index: TAB_INDICES.LINEUP,
  //   label: "Đội Hình",
  //   icon: "/icon/doi-hinh.svg",
  //   iconActive: "/icon/doi-hinh-active.svg"
  // },
  // {
  //   index: TAB_INDICES.ODDS,
  //   label: "Tỷ Lệ",
  //   icon: "/icon/ti-le-keo.svg",
  //   iconActive: "/icon/ty-le-active.svg"
  // },
  // {
  //   index: TAB_INDICES.HEAD_TO_HEAD,
  //   label: "Đối Đầu",
  //   icon: "/icon/doi-dau.svg",
  //   iconActive: "/icon/doi-dau-active.svg"
  // },
  // {
  //   index: TAB_INDICES.INFO,
  //   label: "Thông Tin",
  //   icon: "/icon/bxh.svg",
  //   iconActive: "/icon/thong-tin-active.svg"
  // },
];

/**
 * Mobile tabs configuration (with "Trận đấu liên quan")
 */
export const MOBILE_TABS: TabItem[] = [
  ...DESKTOP_TABS,
  {
    index: TAB_INDICES.RELATED_MATCHES,
    label: "Trận đấu liên quan",
    icon: "/icon/tran-khac.svg",
    iconActive: "/icon/tran-khac-active.svg",
  },
];

/**
 * Get tabs configuration based on device type
 */
export const getTabsConfig = (isMobile: boolean): TabItem[] => {
  return isMobile ? MOBILE_TABS : DESKTOP_TABS;
};

/**
 * Get tab by index
 */
export const getTabByIndex = (
  index: number,
  isMobile: boolean
): TabItem | undefined => {
  const tabs = getTabsConfig(isMobile);
  return tabs.find((tab) => tab.index === index);
};

/**
 * Check if tab index is valid
 */
export const isValidTabIndex = (index: number): boolean => {
  return Object.values(TabIndex).includes(index);
};
