// Category Types
export interface Category {
  id: string;
  name: string;
  slug: string;
  description?: string;
  thumbnail?: string;
  is_active: boolean;
  sort_order: number;
  created_at: string;
  updated_at: string;
}

export interface CreateCategoryRequest {
  name: string;
  slug: string;
  description?: string;
  thumbnail?: string;
  is_active?: boolean;
  sort_order?: number;
}

export interface UpdateCategoryRequest {
  name?: string;
  slug?: string;
  description?: string;
  thumbnail?: string;
  is_active?: boolean;
  sort_order?: number;
}

export interface CategoryFilters {
  is_active?: boolean;
  search?: string;
}

// Post-Category relationship
export interface PostCategory {
  id: string;
  post_id: string;
  category_id: string;
  created_at: string;
}

export interface PostWithCategories {
  id: string;
  title: string;
  content: string;
  thumbnail?: string;
  published_at?: string;
  created_at: string;
  updated_at: string;
  categories: Category[];
}

export interface CategoryWithPostCount extends Category {
  post_count: number;
}

// Validation schemas
export interface CategoryValidationSchema {
  name: string;
  slug: string;
  description?: string;
  thumbnail?: string;
  is_active?: boolean;
  sort_order?: number;
}