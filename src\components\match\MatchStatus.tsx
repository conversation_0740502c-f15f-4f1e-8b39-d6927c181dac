"use client";

import { MatchData, StatisticsData, StatisticsResponse } from "@/types/match";
import { useEffect, useState } from "react";

import Image from "next/image";

export default function MatchStatus({ match }: { match: MatchData }) {
  const [statistics, setStatistics] = useState<StatisticsData | null>(null);
  const [statsLoading, setStatsLoading] = useState(false);
  // Process statistics from match data directly
  useEffect(() => {
    if (
      match.statistics &&
      Array.isArray(match.statistics) &&
      match.statistics.length > 0
    ) {
      const fullMatchStats =
        match.statistics.find((stat: StatisticsResponse) => stat.type === 0) ||
        match.statistics[0];

      if (
        fullMatchStats &&
        fullMatchStats.stats &&
        fullMatchStats.stats.length >= 2
      ) {
        const homeStats = fullMatchStats.stats[0];
        const awayStats = fullMatchStats.stats[1];

        const convertedStats: StatisticsData = {
          corners: {
            home: homeStats.corner_kicks || 0,
            away: awayStats.corner_kicks || 0,
          },
          yellowCards: {
            home: homeStats.yellow_cards || 0,
            away: awayStats.yellow_cards || 0,
          },
          goals: {
            home: homeStats.goals || 0,
            away: awayStats.goals || 0,
          },
        };
        setStatistics(convertedStats);
        setStatsLoading(false);
      } else {
        setStatistics(null);
        setStatsLoading(false);
      }
    } else {
      setStatistics(null);
      setStatsLoading(false);
    }
  }, [match.statistics]);

  const getHalfTimeScore = () => {
    if (match.statistics && Array.isArray(match.statistics)) {
      const halfTimeStats = match.statistics.find(
        (s: StatisticsResponse) => s.type === 1
      );

      if (halfTimeStats && halfTimeStats.stats.length >= 2) {
        const homeHT = halfTimeStats.stats[0].goals || 0;
        const awayHT = halfTimeStats.stats[1].goals || 0;
        return { home: homeHT, away: awayHT };
      }
    }

    // Fallback
    return {
      home: match.homeTeam?.score || 0,
      away: match.awayTeam?.score || 0,
    };
  };

  const halfTimeScore = getHalfTimeScore();

  // Loading state
  if (statsLoading) {
    return (
      <div className="flex md:justify-evenly items-center dark:bg-white justify-center gap-2 [&_div]:px-1 my-4 lg:mb-3 relative z-10 border border-gray-700 border-opacity-25 rounded-3xl px-2 py-1 lg:mx-[100px] sm:mx-[80px] mx-10 dark:divide-x-2 divide-yellow-600 divide-x-2">
        <div className="flex gap-2">
          <p className="p-1 text-green-500 text-xs font-semibold">HT</p>
          <span className="dark:text-black">...</span>
        </div>
        <div className="flex gap-2">
          <Image
            src="/icon/corner.svg"
            alt="corner_kick"
            width={18}
            height={18}
          />
          <span className="dark:text-black">...</span>
        </div>
        <div className="flex gap-2">
          <Image
            src="/icon/yellow.svg"
            alt="yellow_card"
            width={18}
            height={18}
          />
          <span className="dark:text-black">...</span>
        </div>
      </div>
    );
  }

  return (
    <div className="flex md:justify-evenly items-center dark:bg-white justify-center gap-2 [&_div]:px-1 my-4 lg:mb-3 relative z-10 border border-gray-700 border-opacity-25 rounded-3xl py-1 mx-10 dark:divide-x-2 divide-yellow-600 divide-x-2">
      {/* Half Time Score */}
      <div className="flex gap-2">
        <p className="p-1 text-green-500 text-xs font-semibold">HT</p>
        <span className="dark:text-black">
          {halfTimeScore.home} - {halfTimeScore.away}
        </span>
      </div>

      {/* Corner Kicks */}
      <div className="flex gap-2">
        <Image
          src="/icon/corner.svg"
          alt="corner_kick"
          width={18}
          height={18}
        />
        <span className="dark:text-black">
          {statistics?.corners?.home || 0} - {statistics?.corners?.away || 0}
        </span>
      </div>

      {/* Yellow Cards */}
      <div className="flex gap-2">
        <Image
          src="/icon/yellow.svg"
          alt="yellow_card"
          width={18}
          height={18}
        />
        <span className="dark:text-black">
          {statistics?.yellowCards?.home || 0} -{" "}
          {statistics?.yellowCards?.away || 0}
        </span>
      </div>
    </div>
  );
}
