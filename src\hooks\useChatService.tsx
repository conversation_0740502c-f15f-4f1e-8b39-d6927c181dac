import { useSupabase } from "@/contexts/SupabaseContext";
import { ChatService } from "@/services/chat.service";
import type { ChatMessage, ChatRoom } from "@/types";
import { useCallback, useEffect, useRef, useState } from "react";

export function useChatService(userId: string) {
  const { supabase, session } = useSupabase();
  const [chatRooms, setChatRooms] = useState<ChatRoom[]>([]);
  const [messages, setMessages] = useState<ChatMessage[]>([]);
  const [loading, setLoading] = useState(false);
  const [messagesLoading, setMessagesLoading] = useState(false);
  const subscriptionRef = useRef<(() => void) | null>(null);

  // Load messages for a specific room
  const loadMessages = useCallback(
    async (roomId: string) => {
      try {
        setMessagesLoading(true);
        const chatService = new ChatService(supabase);
        const { messages, error } = await chatService.getMessages(roomId);
        if (error) {
          // Error loading messages
          return;
        }
        setMessages(messages);
      } catch (error) {
        console.error("Error loading messages:", error);
      } finally {
        setMessagesLoading(false);
      }
    },
    [supabase]
  );

  // Load chat rooms
  const loadChatRooms = useCallback(async () => {
    if (!userId) {
      return;
    }

    try {
      setLoading(true);
      const chatService = new ChatService(supabase);
      const { rooms, error } = await chatService.getUserChatRooms(userId);
      if (error) {
        // Error loading chat rooms
        return;
      }
      setChatRooms(rooms);

      // Load messages for the first room if available
      if (rooms.length > 0) {
        await loadMessages(rooms[0].id);
      }
    } catch (error) {
      console.error("Error loading chat rooms:", error);
    } finally {
      setLoading(false);
    }
  }, [userId, supabase, loadMessages]);

  // Force reload chat rooms (bypass hasLoaded check)
  const reloadChatRooms = useCallback(async () => {
    if (!userId) {
      return;
    }

    try {
      setLoading(true);
      const chatService = new ChatService(supabase);
      const { rooms, error } = await chatService.getUserChatRooms(userId);
      if (error) {
        // Error reloading chat rooms
        return;
      }
      setChatRooms(rooms);

      // Load messages for the first room if available
      if (rooms.length > 0) {
        await loadMessages(rooms[0].id);
      }
    } catch (error) {
      console.error("Error reloading chat rooms:", error);
    } finally {
      setLoading(false);
    }
  }, [userId, supabase, loadMessages]);

  // Load messages for a specific room by name or type
  const loadMessagesForRoom = useCallback(
    async (roomIdentifier: string) => {
      if (roomIdentifier) {
        await loadMessages(roomIdentifier);
      } else {
      }
    },
    [loadMessages]
  );

  // Send a message
  const sendMessage = useCallback(
    async (roomId: string, content: string) => {
      if (!userId) {
        return { error: new Error("User not authenticated") };
      }

      // Validate userId is a valid UUID
      const uuidRegex =
        /^[0-9a-f]{8}-[0-9a-f]{4}-[1-5][0-9a-f]{3}-[89ab][0-9a-f]{3}-[0-9a-f]{12}$/i;
      if (!uuidRegex.test(userId)) {
        // Invalid userId (not a UUID)
        return { error: new Error("Invalid user ID") };
      }
      try {
        const chatService = new ChatService(supabase);
        const { error } = await chatService.sendMessage(
          roomId,
          userId,
          content
        );
        if (error) {
          // Error sending message
          return { error };
        }
        return { error: null };
      } catch (error) {
        console.error("Error sending message:", error);
        return {
          error: error instanceof Error ? error : new Error("Unknown error"),
        };
      }
    },
    [supabase, userId]
  );

  // Create a new chat room
  const createChat = useCallback(
    async (name: string, type: "direct" | "group" | "private") => {
      if (!userId) return { error: new Error("User not authenticated") };

      try {
        const chatService = new ChatService(supabase);
        const { data, error } = await chatService.createChat(
          { name, type },
          userId
        );
        if (error) {
          // Error creating chat
          return { error };
        }

        // Reload chat rooms to include the new room
        await loadChatRooms();

        return { room: data, error: null };
      } catch (error) {
        console.error("Error creating chat:", error);
        return {
          error: error instanceof Error ? error : new Error("Unknown error"),
        };
      }
    },
    [userId, supabase, loadChatRooms]
  );

  // Delete a chat room
  const deleteChat = useCallback(
    async (roomId: string) => {
      try {
        const chatService = new ChatService(supabase);
        const { error } = await chatService.deleteChat(roomId);
        if (error) {
          // Error deleting chat
          return { error };
        }

        // Reload chat rooms to remove the deleted room
        await loadChatRooms();

        return { error: null };
      } catch (error) {
        console.error("Error deleting chat:", error);
        return {
          error: error instanceof Error ? error : new Error("Unknown error"),
        };
      }
    },
    [loadChatRooms, supabase]
  );

  // Join a chat room
  const joinChat = useCallback(
    async (roomId: string) => {
      if (!userId) return { error: new Error("User not authenticated") };

      try {
        const chatService = new ChatService(supabase);
        const { error } = await chatService.joinChat(roomId, userId);
        if (error) {
          // Error joining chat
          return { error };
        }

        // Reload chat rooms to include the joined room
        await loadChatRooms();

        return { error: null };
      } catch (error) {
        console.error("Error joining chat:", error);
        return {
          error: error instanceof Error ? error : new Error("Unknown error"),
        };
      }
    },
    [userId, supabase, loadChatRooms]
  );

  // Reset state when userId changes
  useEffect(() => {
    setChatRooms([]);
    setLoading(false);
    if (subscriptionRef.current) {
      subscriptionRef.current();
      subscriptionRef.current = null;
    }
  }, [userId]);

  // Cleanup subscription on unmount
  useEffect(() => {
    return () => {
      if (subscriptionRef.current) {
        subscriptionRef.current();
      }
    };
  }, []);

  return {
    chatRooms,
    messages,
    loading,
    messagesLoading,
    loadChatRooms,
    reloadChatRooms,
    loadMessages,
    loadMessagesForRoom,
    sendMessage,
    createChat,
    deleteChat,
    joinChat,
  };
}
