import { clsx, type ClassValue } from "clsx";
import { twMerge } from "tailwind-merge";

export function cn(...inputs: ClassValue[]) {
  return twMerge(clsx(inputs));
}


export const formatTime = (timeString: string) => {
  if (!timeString) return '';
  
  try {
    // Parse the time string "11/06/2025 16:00:00" and format to "11/06/2025 16:00"
    const timeMatch = timeString.match(/^(.+?)\s+(\d{1,2}):(\d{2}):\d{2}$/);
    if (timeMatch) {
      const datePart = timeMatch[1]; // "11/06/2025"
      const hours = timeMatch[2];     // "16"
      const minutes = timeMatch[3];   // "00"
      return `${datePart} ${hours}:${minutes}`;
    }
    
    // Fallback: try to extract time from different formats
    const date = new Date(timeString);
    if (!isNaN(date.getTime())) {
      const dateStr = date.toLocaleDateString('vi-VN');
      const timeStr = date.toLocaleTimeString('vi-VN', { 
        hour: '2-digit', 
        minute: '2-digit',
        hour12: false 
      });
      return `${dateStr} ${timeStr}`;
    }
    
    return timeString;
  } catch (error) {
    console.warn('Error formatting time:', timeString, error);
    return timeString;
  }
};


export const getStatusText = (status: string) => {
  switch (status.toLowerCase()) {
    case 'live':
      return 'Trực tiếp';
    case 'pending':
      return 'Chưa diễn ra';
    case 'upcoming':
      return 'Sắp tới';
    case 'finished':
      return 'Đã kết thúc';
    case 'ht':
    case 'halftime':
      return 'HT';
    default:
      return status;
  }
};