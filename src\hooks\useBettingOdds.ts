import { useSupabase } from "@/contexts/SupabaseContext";
import { BettingService } from "@/services/betting.service";
import { useCallback, useEffect, useMemo, useState } from "react";

import { BettingOdds } from "@/types/betting.types";

const useBettingOdds = () => {
  const [bettingOdds, setBettingOdds] = useState<BettingOdds[]>([]);
  const [loading, setLoading] = useState<boolean>(false);
  const { supabase } = useSupabase();

  const getBettingOdds = useCallback(async () => {
    setLoading(true);
    const bettingService = new BettingService(supabase);
    const data = await bettingService.getWinningBettingOdds();
    if (data) {
      setBettingOdds(data);
    }
    setLoading(false);
  }, [supabase]);

  useEffect(() => {
    getBettingOdds();
  }, [getBettingOdds]);

  const winningBettingOdds = useMemo(() => {
    return bettingOdds.filter((odd) => odd.status === "win");
  }, [bettingOdds]);

  return {
    bettingOdds,
    loading,
    winningBettingOdds,
    getBettingOdds,
  };
};

export default useBettingOdds;
