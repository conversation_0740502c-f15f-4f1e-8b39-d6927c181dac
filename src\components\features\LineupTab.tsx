"use client";

interface LineupTabProps {
  isLoading?: boolean;
}

export default function LineupTab({ isLoading = false }: LineupTabProps) {
  // Loading skeleton component
  const LoadingSkeleton = () => (
    <div className="h-full space-y-6 p-6">
      {/* Manchester United Formation Skeleton */}
      <div className="bg-white dark:bg-custom-dark rounded-lg p-6 border border-gray-200 dark:border-gray-700">
        <div className="h-6 bg-gray-200 dark:bg-gray-700 rounded-lg mb-4 animate-pulse"></div>
        <div className="text-center py-8">
          <div className="space-y-3">
            <div className="w-32 h-4 bg-gray-200 dark:bg-gray-700 rounded mx-auto animate-pulse"></div>
            <div className="w-48 h-3 bg-gray-200 dark:bg-gray-700 rounded mx-auto animate-pulse"></div>
          </div>
        </div>
      </div>

      {/* Liverpool Formation Skeleton */}
      <div className="bg-white dark:bg-custom-dark rounded-lg p-6 border border-gray-200 dark:border-gray-700">
        <div className="h-6 bg-gray-200 dark:bg-gray-700 rounded-lg mb-4 animate-pulse"></div>
        <div className="text-center py-8">
          <div className="space-y-3">
            <div className="w-32 h-4 bg-gray-200 dark:bg-gray-700 rounded mx-auto animate-pulse"></div>
            <div className="w-48 h-3 bg-gray-200 dark:bg-gray-700 rounded mx-auto animate-pulse"></div>
          </div>
        </div>
      </div>

      {/* Substitutes Skeleton */}
      <div className="bg-white dark:bg-custom-dark rounded-lg p-6 border border-gray-200 dark:border-gray-700">
        <div className="h-6 bg-gray-200 dark:bg-gray-700 rounded-lg mb-4 animate-pulse"></div>
        <div className="text-center py-8">
          <div className="space-y-3">
            <div className="w-32 h-4 bg-gray-200 dark:bg-gray-700 rounded mx-auto animate-pulse"></div>
            <div className="w-48 h-3 bg-gray-200 dark:bg-gray-700 rounded mx-auto animate-pulse"></div>
          </div>
        </div>
      </div>
    </div>
  );

  // Show skeleton when loading
  if (isLoading) {
    return <LoadingSkeleton />;
  }

  return (
    <div className="h-full space-y-6 p-6">
      {/* Manchester United Formation */}
    </div>
  );
} 