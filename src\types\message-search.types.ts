export interface MessageSearchProps {
    onSearch: (query: string, filters?: SearchFilters) => void;
    onClose: () => void;
    searchResults?: {
        total: number;
        current: number;
    };
    onNavigate?: (direction: "prev" | "next") => void;
}

export interface SearchFilters {
    dateRange?: "today" | "week" | "month" | "all";
    fileType?: "text" | "files" | "images" | "all";
    sender?: string;
}